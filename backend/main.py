"""
Nexus - Crypto Causal Event Correlation Engine
Main FastAPI application entry point
"""

import os
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.core.config import settings
from app.core.database import init_db
from app.api.routes import api_router
from app.websocket.endpoints import ws_router
from app.services.realtime_service import realtime_service
from app.core.logging_config import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Nexus application...")
    try:
        await init_db()
        logger.info("Database initialized successfully")

        # Start real-time broadcasting service
        await realtime_service.start_broadcasting()
        logger.info("Real-time broadcasting service started")

    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        raise

    yield

    # Shutdown
    logger.info("Shutting down Nexus application...")

    # Stop real-time broadcasting service
    try:
        await realtime_service.stop_broadcasting()
        logger.info("Real-time broadcasting service stopped")
    except Exception as e:
        logger.error(f"Error stopping real-time service: {e}")


# Create FastAPI application
app = FastAPI(
    title="Nexus API",
    description="Crypto Causal Event Correlation Engine",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# Include WebSocket routes
app.include_router(ws_router)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Nexus - Crypto Causal Event Correlation Engine",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": "2025-01-01T00:00:00Z"  # Will be replaced with actual timestamp
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
