#!/usr/bin/env python3
"""
Debug pattern detection calculations
"""

from datetime import datetime, timedelta


def calculate_price_change(market_data, minutes=60):
    """Debug version of price change calculation"""
    if len(market_data) < 2:
        return None
    
    # Sort by timestamp
    sorted_data = sorted(market_data, key=lambda x: x['timestamp'])
    
    # Find data points for comparison
    latest = sorted_data[-1]
    cutoff_time = latest['timestamp'] - timedelta(minutes=minutes)
    
    # Find earliest point within the time window
    earliest = None
    for data_point in sorted_data:
        if data_point['timestamp'] >= cutoff_time:
            earliest = data_point
            break
    
    if not earliest or earliest['price'] is None or latest['price'] is None:
        return None
    
    if earliest['price'] == 0:
        return None
    
    change = ((latest['price'] - earliest['price']) / earliest['price']) * 100
    print(f"Price change calculation:")
    print(f"  Earliest price: ${earliest['price']} at {earliest['timestamp']}")
    print(f"  Latest price: ${latest['price']} at {latest['timestamp']}")
    print(f"  Change: {change:.2f}%")
    
    return change


def calculate_volume_ratio(market_data, minutes=60):
    """Debug version of volume ratio calculation"""
    if len(market_data) < 2:
        return None
    
    # Sort by timestamp
    sorted_data = sorted(market_data, key=lambda x: x['timestamp'])
    
    # Get recent volume (last data point)
    latest = sorted_data[-1]
    if latest['volume'] is None:
        return None
    
    # Calculate average volume from historical data
    historical_volumes = []
    for data_point in sorted_data[:-1]:  # Exclude latest
        if data_point['volume'] is not None:
            historical_volumes.append(data_point['volume'])
    
    if not historical_volumes:
        return None
    
    avg_volume = sum(historical_volumes) / len(historical_volumes)
    if avg_volume == 0:
        return None
    
    ratio = latest['volume'] / avg_volume
    print(f"Volume ratio calculation:")
    print(f"  Historical average volume: {avg_volume:,.0f}")
    print(f"  Latest volume: {latest['volume']:,.0f}")
    print(f"  Ratio: {ratio:.2f}x")
    
    return ratio


def test_cex_listing_thresholds():
    """Test CEX listing detection with debug output"""
    print("🧪 Testing CEX Listing Detection Thresholds")
    
    now = datetime.now()
    
    # Test data with 30% price increase and clear 5x volume spike
    market_data = [
        {
            'timestamp': now - timedelta(minutes=120),
            'price': 100.0,
            'volume': 1000000,
        },
        {
            'timestamp': now - timedelta(minutes=90),
            'price': 102.0,
            'volume': 1100000,
        },
        {
            'timestamp': now - timedelta(minutes=60),
            'price': 105.0,
            'volume': 1200000,
        },
        {
            'timestamp': now - timedelta(minutes=30),
            'price': 125.0,
            'volume': 1300000,
        },
        {
            'timestamp': now,
            'price': 130.0,  # 24% increase from 60min ago
            'volume': 6000000,  # 5x the average historical volume
        }
    ]
    
    print(f"\nMarket data points: {len(market_data)}")
    for i, data in enumerate(market_data):
        print(f"  {i+1}. {data['timestamp'].strftime('%H:%M:%S')}: ${data['price']}, Vol: {data['volume']:,}")
    
    # Calculate metrics
    price_change = calculate_price_change(market_data, 60)
    volume_ratio = calculate_volume_ratio(market_data, 60)
    
    print(f"\nThreshold checks:")
    print(f"  Price change: {price_change:.1f}% (need >15%): {'✅' if price_change and price_change > 15 else '❌'}")
    print(f"  Volume ratio: {volume_ratio:.1f}x (need >3x): {'✅' if volume_ratio and volume_ratio > 3 else '❌'}")
    
    # Test confidence calculation
    if price_change and volume_ratio:
        # Simplified confidence calculation
        price_factor = min(1.0, (price_change - 15) / 35 + 0.5)
        volume_factor = min(1.0, (volume_ratio - 3) / 7 + 0.5)
        
        print(f"\nConfidence factors:")
        print(f"  Price factor: {price_factor:.3f}")
        print(f"  Volume factor: {volume_factor:.3f}")
        
        # Simplified confidence (without social mentions)
        confidence = (price_factor * 0.35 + volume_factor * 0.30 + 0.0 * 0.25 + 0.8 * 0.10)
        print(f"  Overall confidence: {confidence:.3f} (need >0.7)")
        
        if confidence > 0.7:
            print("✅ Pattern would be detected!")
        else:
            print("❌ Confidence too low for detection")
    
    return price_change, volume_ratio


def test_whale_accumulation_thresholds():
    """Test whale accumulation detection"""
    print("\n🧪 Testing Whale Accumulation Detection Thresholds")
    
    now = datetime.now()
    
    # Test data with stable price and high volume
    market_data = [
        {
            'timestamp': now - timedelta(minutes=300),
            'price': 50.0,
            'volume': 800000,
        },
        {
            'timestamp': now - timedelta(minutes=240),
            'price': 50.1,
            'volume': 900000,
        },
        {
            'timestamp': now - timedelta(minutes=180),
            'price': 49.9,
            'volume': 1000000,
        },
        {
            'timestamp': now - timedelta(minutes=120),
            'price': 50.0,
            'volume': 1100000,
        },
        {
            'timestamp': now - timedelta(minutes=60),
            'price': 50.2,
            'volume': 1200000,
        },
        {
            'timestamp': now,
            'price': 50.1,
            'volume': 2400000,
        }
    ]
    
    print(f"\nMarket data points: {len(market_data)}")
    for i, data in enumerate(market_data):
        print(f"  {i+1}. {data['timestamp'].strftime('%H:%M:%S')}: ${data['price']}, Vol: {data['volume']:,}")
    
    # Calculate volume ratio
    volume_ratio = calculate_volume_ratio(market_data, 240)
    
    # Calculate price volatility
    prices = [d['price'] for d in market_data]
    mean_price = sum(prices) / len(prices)
    variance = sum((p - mean_price) ** 2 for p in prices) / len(prices)
    std_dev = variance ** 0.5
    volatility = std_dev / mean_price if mean_price > 0 else 0
    
    print(f"\nVolatility calculation:")
    print(f"  Mean price: ${mean_price:.2f}")
    print(f"  Standard deviation: ${std_dev:.3f}")
    print(f"  Coefficient of variation: {volatility:.3f}")
    
    print(f"\nThreshold checks:")
    print(f"  Volume ratio: {volume_ratio:.1f}x (need >2x): {'✅' if volume_ratio and volume_ratio > 2 else '❌'}")
    print(f"  Price volatility: {volatility:.3f} (need <0.05): {'✅' if volatility < 0.05 else '❌'}")


if __name__ == "__main__":
    test_cex_listing_thresholds()
    test_whale_accumulation_thresholds()
