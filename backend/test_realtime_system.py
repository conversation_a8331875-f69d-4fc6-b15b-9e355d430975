#!/usr/bin/env python3
"""
Real-time System Integration Test

Tests the complete real-time WebSocket infrastructure including:
- WebSocket connections
- Real-time data broadcasting
- Pattern alert broadcasting
- System status updates
"""

import asyncio
import json
import websockets
import aiohttp
import logging
from datetime import datetime
import sys

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

API_BASE = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"


class RealtimeSystemTester:
    """Comprehensive real-time system tester"""
    
    def __init__(self):
        self.websocket = None
        self.received_messages = []
        self.test_results = {}
    
    async def run_all_tests(self):
        """Run all real-time system tests"""
        logger.info("🚀 Starting Real-time System Integration Tests")
        
        tests = [
            ("API Endpoints", self.test_api_endpoints),
            ("WebSocket Connection", self.test_websocket_connection),
            ("Real-time Broadcasting", self.test_realtime_broadcasting),
            ("System Status Updates", self.test_system_status),
            ("Pattern Alert Simulation", self.test_pattern_alerts),
            ("Connection Management", self.test_connection_management)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n🧪 Testing: {test_name}")
                result = await test_func()
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: FAILED")
                self.test_results[test_name] = result
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
        
        # Summary
        logger.info(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All real-time system tests passed!")
            return True
        else:
            logger.error("❌ Some tests failed")
            return False
    
    async def test_api_endpoints(self):
        """Test real-time API endpoints"""
        try:
            async with aiohttp.ClientSession() as session:
                # Test real-time status endpoint
                async with session.get(f"{API_BASE}/api/v1/realtime/status") as resp:
                    if resp.status != 200:
                        return False
                    data = await resp.json()
                    
                    # Verify response structure
                    required_keys = ['broadcasting', 'websocket', 'endpoints']
                    if not all(key in data for key in required_keys):
                        logger.error("Missing required keys in status response")
                        return False
                    
                    logger.info(f"Broadcasting status: {data['broadcasting']['is_running']}")
                    logger.info(f"Active connections: {data['websocket']['total_connections']}")
                
                # Test WebSocket stats endpoint
                async with session.get(f"{API_BASE}/ws/stats") as resp:
                    if resp.status != 200:
                        return False
                    stats = await resp.json()
                    logger.info(f"WebSocket stats: {stats}")
                
                return True
        
        except Exception as e:
            logger.error(f"API endpoint test error: {e}")
            return False
    
    async def test_websocket_connection(self):
        """Test WebSocket connection and basic communication"""
        try:
            # Connect to WebSocket
            self.websocket = await websockets.connect(WS_URL)
            logger.info("WebSocket connected successfully")
            
            # Wait for connection established message
            message = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            data = json.loads(message)
            
            if data.get('type') != 'connection_established':
                logger.error(f"Expected connection_established, got: {data.get('type')}")
                return False
            
            logger.info(f"Connection established with client ID: {data.get('client_id')}")
            
            # Test ping-pong
            ping_message = {"type": "ping"}
            await self.websocket.send(json.dumps(ping_message))
            
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            pong_data = json.loads(response)
            
            if pong_data.get('type') != 'pong':
                logger.error(f"Expected pong, got: {pong_data.get('type')}")
                return False
            
            logger.info("Ping-pong test successful")
            return True
        
        except Exception as e:
            logger.error(f"WebSocket connection test error: {e}")
            return False
    
    async def test_realtime_broadcasting(self):
        """Test real-time broadcasting functionality"""
        try:
            if not self.websocket:
                logger.error("WebSocket not connected")
                return False
            
            # Send test broadcast via API
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{API_BASE}/api/v1/realtime/broadcast/test") as resp:
                    if resp.status != 200:
                        logger.error(f"Broadcast API failed: {resp.status}")
                        return False
                    
                    broadcast_result = await resp.json()
                    logger.info(f"Broadcast sent to {broadcast_result['recipients']} recipients")
            
            # Wait for broadcast message
            message = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            data = json.loads(message)
            
            if data.get('type') != 'test_message':
                logger.error(f"Expected test_message, got: {data.get('type')}")
                return False
            
            logger.info("Test broadcast received successfully")
            self.received_messages.append(data)
            return True
        
        except Exception as e:
            logger.error(f"Real-time broadcasting test error: {e}")
            return False
    
    async def test_system_status(self):
        """Test system status request and response"""
        try:
            if not self.websocket:
                logger.error("WebSocket not connected")
                return False
            
            # Request system status
            status_request = {"type": "get_status"}
            await self.websocket.send(json.dumps(status_request))
            
            # Wait for status response
            message = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            data = json.loads(message)
            
            if data.get('type') != 'system_status':
                logger.error(f"Expected system_status, got: {data.get('type')}")
                return False
            
            status = data.get('status', {})
            logger.info(f"System status received:")
            logger.info(f"  Data collection: {status.get('data_collection', {})}")
            logger.info(f"  Pattern detection: {status.get('pattern_detection', {})}")
            logger.info(f"  WebSocket: {status.get('websocket', {})}")
            
            self.received_messages.append(data)
            return True
        
        except Exception as e:
            logger.error(f"System status test error: {e}")
            return False
    
    async def test_pattern_alerts(self):
        """Test pattern alert functionality"""
        try:
            if not self.websocket:
                logger.error("WebSocket not connected")
                return False
            
            # Request recent patterns
            patterns_request = {"type": "get_recent_patterns", "hours": 24}
            await self.websocket.send(json.dumps(patterns_request))
            
            # Wait for patterns response
            message = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            data = json.loads(message)
            
            if data.get('type') != 'recent_patterns':
                logger.error(f"Expected recent_patterns, got: {data.get('type')}")
                return False
            
            patterns = data.get('patterns', [])
            logger.info(f"Received {len(patterns)} recent patterns")
            
            self.received_messages.append(data)
            return True
        
        except Exception as e:
            logger.error(f"Pattern alerts test error: {e}")
            return False
    
    async def test_connection_management(self):
        """Test connection management and cleanup"""
        try:
            # Check connection stats before closing
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{API_BASE}/ws/stats") as resp:
                    stats_before = await resp.json()
                    connections_before = stats_before['total_connections']
                    logger.info(f"Connections before close: {connections_before}")
            
            # Close WebSocket connection
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
                logger.info("WebSocket connection closed")
            
            # Wait a moment for cleanup
            await asyncio.sleep(2)
            
            # Check connection stats after closing
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{API_BASE}/ws/stats") as resp:
                    stats_after = await resp.json()
                    connections_after = stats_after['total_connections']
                    logger.info(f"Connections after close: {connections_after}")
            
            # Verify connection was cleaned up
            if connections_after >= connections_before:
                logger.error("Connection not properly cleaned up")
                return False
            
            logger.info("Connection cleanup successful")
            return True
        
        except Exception as e:
            logger.error(f"Connection management test error: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup test resources"""
        if self.websocket:
            await self.websocket.close()
        
        logger.info(f"\n📋 Test Summary:")
        logger.info(f"Total messages received: {len(self.received_messages)}")
        for i, msg in enumerate(self.received_messages):
            logger.info(f"  {i+1}. {msg.get('type', 'unknown')}")


async def main():
    """Run real-time system tests"""
    tester = RealtimeSystemTester()
    
    try:
        success = await tester.run_all_tests()
        return 0 if success else 1
    
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        return 1
    
    except Exception as e:
        logger.error(f"Test execution error: {e}")
        return 1
    
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
