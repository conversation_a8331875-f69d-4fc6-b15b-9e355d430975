# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
asyncpg==0.29.0
sqlalchemy==2.0.23
alembic==1.12.1

# Redis and Celery
redis==5.0.1
celery==5.3.4

# HTTP clients and WebSocket
httpx==0.25.2
websockets==12.0
aiohttp==3.9.1

# Data processing
pandas==2.1.4
numpy==1.25.2
python-dateutil==2.8.2

# Environment and configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Monitoring and logging
prometheus-client==0.19.0
structlog==23.2.0

# API clients for data sources
pycoingecko==3.1.0
praw==7.7.1  # Reddit API
tweepy==4.14.0  # Twitter API
PyGithub==2.1.1  # GitHub API

# WebSocket client for Binance
websocket-client==1.6.4

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2  # for testing FastAPI

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
