#!/usr/bin/env python3
"""
Enterprise-Grade WebSocket Load Testing

Tests system performance under high concurrent load:
- 50+ concurrent WebSocket connections
- Sustained message broadcasting
- Connection stability under stress
- Resource utilization monitoring
"""

import asyncio
import json
import websockets
import aiohttp
import time
import psutil
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import statistics
import sys

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

WS_URL = "ws://localhost:8000/ws"
API_BASE = "http://localhost:8000"


class WebSocketLoadTester:
    """Enterprise-grade WebSocket load tester"""
    
    def __init__(self, num_connections: int = 50):
        self.num_connections = num_connections
        self.connections: List[websockets.WebSocketServerProtocol] = []
        self.connection_stats: Dict[str, Any] = {}
        self.message_counts: Dict[str, int] = {}
        self.response_times: List[float] = []
        self.errors: List[str] = []
        self.start_time = None
        self.test_duration = 300  # 5 minutes
        
    async def run_load_test(self) -> bool:
        """Run comprehensive WebSocket load test"""
        logger.info(f"🚀 Starting WebSocket Load Test: {self.num_connections} connections")
        
        try:
            # Start system monitoring
            monitor_task = asyncio.create_task(self._monitor_system_resources())
            
            # Phase 1: Connection establishment
            logger.info("Phase 1: Establishing connections...")
            connection_success = await self._establish_connections()
            if not connection_success:
                return False
            
            # Phase 2: Sustained load testing
            logger.info("Phase 2: Sustained load testing...")
            load_success = await self._sustained_load_test()
            
            # Phase 3: Stress testing
            logger.info("Phase 3: Stress testing...")
            stress_success = await self._stress_test()
            
            # Phase 4: Graceful shutdown
            logger.info("Phase 4: Graceful shutdown...")
            await self._cleanup_connections()
            
            # Stop monitoring
            monitor_task.cancel()
            
            # Generate report
            self._generate_load_test_report()
            
            return connection_success and load_success and stress_success
            
        except Exception as e:
            logger.error(f"Load test failed: {e}")
            return False
    
    async def _establish_connections(self) -> bool:
        """Establish all WebSocket connections"""
        self.start_time = time.time()
        connection_tasks = []
        
        for i in range(self.num_connections):
            task = asyncio.create_task(self._create_connection(f"client_{i}"))
            connection_tasks.append(task)
        
        # Wait for all connections with timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*connection_tasks, return_exceptions=True),
                timeout=30.0
            )
            
            successful_connections = sum(1 for r in results if r is True)
            logger.info(f"Established {successful_connections}/{self.num_connections} connections")
            
            if successful_connections < self.num_connections * 0.95:  # 95% success rate required
                logger.error("Failed to establish minimum required connections")
                return False
            
            return True
            
        except asyncio.TimeoutError:
            logger.error("Connection establishment timed out")
            return False
    
    async def _create_connection(self, client_id: str) -> bool:
        """Create a single WebSocket connection"""
        try:
            ws = await websockets.connect(f"{WS_URL}?client_id={client_id}")
            self.connections.append(ws)
            self.message_counts[client_id] = 0
            
            # Wait for connection established message
            message = await asyncio.wait_for(ws.recv(), timeout=5.0)
            data = json.loads(message)
            
            if data.get('type') == 'connection_established':
                self.connection_stats[client_id] = {
                    'connected_at': time.time(),
                    'messages_received': 0,
                    'last_message_time': time.time()
                }
                return True
            
            return False
            
        except Exception as e:
            self.errors.append(f"Connection {client_id}: {str(e)}")
            return False
    
    async def _sustained_load_test(self) -> bool:
        """Run sustained load test for specified duration"""
        test_start = time.time()
        
        # Start message listeners for all connections
        listener_tasks = []
        for i, ws in enumerate(self.connections):
            client_id = f"client_{i}"
            task = asyncio.create_task(self._message_listener(ws, client_id))
            listener_tasks.append(task)
        
        # Start periodic broadcasts
        broadcast_task = asyncio.create_task(self._periodic_broadcasts())
        
        # Run for test duration
        await asyncio.sleep(self.test_duration)
        
        # Stop all tasks
        broadcast_task.cancel()
        for task in listener_tasks:
            task.cancel()
        
        # Wait for cleanup
        await asyncio.gather(*listener_tasks, broadcast_task, return_exceptions=True)
        
        test_duration = time.time() - test_start
        total_messages = sum(self.message_counts.values())
        
        logger.info(f"Sustained load test completed:")
        logger.info(f"  Duration: {test_duration:.2f} seconds")
        logger.info(f"  Total messages: {total_messages}")
        logger.info(f"  Messages/second: {total_messages/test_duration:.2f}")
        logger.info(f"  Active connections: {len(self.connections)}")
        
        # Success criteria: >90% connections active, >1000 total messages
        active_connections = len([c for c in self.connections if not c.closed])
        success_rate = active_connections / len(self.connections)
        
        return success_rate > 0.9 and total_messages > 1000
    
    async def _message_listener(self, ws: websockets.WebSocketServerProtocol, client_id: str):
        """Listen for messages on a WebSocket connection"""
        try:
            while not ws.closed:
                message = await asyncio.wait_for(ws.recv(), timeout=1.0)
                self.message_counts[client_id] += 1
                
                if client_id in self.connection_stats:
                    self.connection_stats[client_id]['messages_received'] += 1
                    self.connection_stats[client_id]['last_message_time'] = time.time()
                
        except asyncio.TimeoutError:
            pass  # Normal timeout, continue listening
        except Exception as e:
            self.errors.append(f"Listener {client_id}: {str(e)}")
    
    async def _periodic_broadcasts(self):
        """Send periodic broadcast requests to generate traffic"""
        try:
            while True:
                # Trigger test broadcast
                async with aiohttp.ClientSession() as session:
                    start_time = time.time()
                    async with session.post(f"{API_BASE}/api/v1/realtime/broadcast/test") as resp:
                        response_time = time.time() - start_time
                        self.response_times.append(response_time)
                        
                        if resp.status != 200:
                            self.errors.append(f"Broadcast failed: {resp.status}")
                
                await asyncio.sleep(5)  # Broadcast every 5 seconds
                
        except asyncio.CancelledError:
            pass
    
    async def _stress_test(self) -> bool:
        """Run stress test with rapid message sending"""
        logger.info("Starting stress test with rapid messaging...")
        
        # Send rapid ping messages from all connections
        ping_tasks = []
        for i, ws in enumerate(self.connections[:10]):  # Use first 10 connections
            if not ws.closed:
                task = asyncio.create_task(self._rapid_ping_test(ws, f"client_{i}"))
                ping_tasks.append(task)
        
        # Run stress test for 30 seconds
        await asyncio.sleep(30)
        
        # Cancel all ping tasks
        for task in ping_tasks:
            task.cancel()
        
        await asyncio.gather(*ping_tasks, return_exceptions=True)
        
        # Check connection stability after stress test
        active_connections = len([c for c in self.connections if not c.closed])
        stability_rate = active_connections / len(self.connections)
        
        logger.info(f"Stress test completed. Connection stability: {stability_rate:.2%}")
        
        return stability_rate > 0.85  # 85% connections should survive stress test
    
    async def _rapid_ping_test(self, ws: websockets.WebSocketServerProtocol, client_id: str):
        """Send rapid ping messages to stress test the connection"""
        try:
            for i in range(100):  # Send 100 rapid pings
                if ws.closed:
                    break
                
                ping_msg = {"type": "ping", "sequence": i}
                await ws.send(json.dumps(ping_msg))
                await asyncio.sleep(0.1)  # 10 pings per second
                
        except Exception as e:
            self.errors.append(f"Rapid ping {client_id}: {str(e)}")
    
    async def _monitor_system_resources(self):
        """Monitor system resource usage during load test"""
        try:
            while True:
                # Get system metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                # Log if resources are high
                if cpu_percent > 80:
                    logger.warning(f"High CPU usage: {cpu_percent:.1f}%")
                
                if memory.percent > 80:
                    logger.warning(f"High memory usage: {memory.percent:.1f}%")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
        except asyncio.CancelledError:
            pass
    
    async def _cleanup_connections(self):
        """Clean up all WebSocket connections"""
        cleanup_tasks = []
        for ws in self.connections:
            if not ws.closed:
                task = asyncio.create_task(ws.close())
                cleanup_tasks.append(task)
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self.connections.clear()
    
    def _generate_load_test_report(self):
        """Generate comprehensive load test report"""
        total_duration = time.time() - self.start_time if self.start_time else 0
        total_messages = sum(self.message_counts.values())
        
        logger.info("\n" + "="*60)
        logger.info("WEBSOCKET LOAD TEST REPORT")
        logger.info("="*60)
        logger.info(f"Test Duration: {total_duration:.2f} seconds")
        logger.info(f"Target Connections: {self.num_connections}")
        logger.info(f"Successful Connections: {len(self.connections)}")
        logger.info(f"Total Messages Processed: {total_messages}")
        logger.info(f"Messages/Second: {total_messages/total_duration:.2f}")
        logger.info(f"Total Errors: {len(self.errors)}")
        
        if self.response_times:
            logger.info(f"Average Response Time: {statistics.mean(self.response_times):.3f}s")
            logger.info(f"95th Percentile Response Time: {statistics.quantiles(self.response_times, n=20)[18]:.3f}s")
        
        if self.errors:
            logger.info("\nErrors encountered:")
            for error in self.errors[:10]:  # Show first 10 errors
                logger.info(f"  - {error}")
        
        logger.info("="*60)


async def main():
    """Run WebSocket load test"""
    tester = WebSocketLoadTester(num_connections=50)
    
    try:
        success = await tester.run_load_test()
        return 0 if success else 1
    
    except KeyboardInterrupt:
        logger.info("Load test interrupted by user")
        return 1
    
    except Exception as e:
        logger.error(f"Load test failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
