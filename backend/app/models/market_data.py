"""
Market data models for time-series data storage
"""

from sqlalchemy import Column, String, Numeric, DateTime, Text, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
import uuid

from app.core.database import Base


class MarketData(Base):
    """Market data time-series table (will be converted to hypertable)"""
    __tablename__ = "market_data"
    
    # Composite primary key for TimescaleDB
    time = Column(DateTime(timezone=True), primary_key=True, nullable=False)
    symbol = Column(String(20), primary_key=True, nullable=False)
    
    # Price and volume data
    price = Column(Numeric(20, 8))
    volume = Column(Numeric(20, 8))
    market_cap = Column(Numeric(20, 2))
    
    # Metadata
    source = Column(String(50))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes for better query performance
    __table_args__ = (
        Index('idx_market_data_time_symbol', 'time', 'symbol'),
        Index('idx_market_data_symbol_time', 'symbol', 'time'),
    )


class Event(Base):
    """Events table for storing various market events"""
    __tablename__ = "events"
    
    # Composite primary key for TimescaleDB
    time = Column(DateTime(timezone=True), primary_key=True, nullable=False)
    event_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Event details
    event_type = Column(String(100), nullable=False)
    symbol = Column(String(20))
    data = Column(JSONB)
    source = Column(String(100))
    confidence_score = Column(Numeric(3, 2))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_events_time_type', 'time', 'event_type'),
        Index('idx_events_symbol_time', 'symbol', 'time'),
    )


class SocialMention(Base):
    """Social media mentions and sentiment data"""
    __tablename__ = "social_mentions"
    
    # Composite primary key for TimescaleDB
    time = Column(DateTime(timezone=True), primary_key=True, nullable=False)
    mention_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Mention details
    symbol = Column(String(20))
    platform = Column(String(20))  # twitter, reddit, etc.
    account = Column(String(100))
    content = Column(Text)
    engagement_score = Column(Numeric(10, 2))
    sentiment_score = Column(Numeric(3, 2))  # -1 to 1
    
    # Metadata
    source_url = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_social_mentions_time_platform', 'time', 'platform'),
        Index('idx_social_mentions_symbol_time', 'symbol', 'time'),
    )
