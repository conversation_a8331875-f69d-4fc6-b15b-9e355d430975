"""
Correlation and pattern detection models
"""

from sqlalchemy import Column, String, Numeric, DateTime, Integer, Boolean, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class Correlation(Base):
    """Detected correlations between events and market movements"""
    __tablename__ = "correlations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Basic correlation info
    symbol = Column(String(20), nullable=False)
    pattern_type = Column(String(100), nullable=False)
    confidence_score = Column(Numeric(3, 2), nullable=False)
    
    # Timing
    trigger_time = Column(DateTime(timezone=True), nullable=False)
    detection_time = Column(DateTime(timezone=True), server_default=func.now())
    
    # Evidence and metadata
    evidence = Column(JSONB)  # Store related events, transactions, etc.
    status = Column(String(20), default='active')  # active, validated, false_positive
    
    # Performance tracking
    price_change_1h = Column(Numeric(5, 2))  # % change after 1 hour
    price_change_24h = Column(Numeric(5, 2))  # % change after 24 hours
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class User(Base):
    """User accounts for authentication and alerts"""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False)
    subscription_tier = Column(String(20), default='free')
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationship to alert configs
    alert_configs = relationship("AlertConfig", back_populates="user")


class AlertConfig(Base):
    """User alert configurations"""
    __tablename__ = "alert_configs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'))
    
    # Alert criteria
    symbol = Column(String(20))
    pattern_types = Column(JSONB)  # Array of pattern types to watch
    min_confidence = Column(Numeric(3, 2), default=0.70)
    
    # Settings
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship
    user = relationship("User", back_populates="alert_configs")


class DataSource(Base):
    """Track data source health and statistics"""
    __tablename__ = "data_sources"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(50), unique=True, nullable=False)
    source_type = Column(String(20), nullable=False)  # api, websocket, scraper
    
    # Health metrics
    is_active = Column(Boolean, default=True)
    last_successful_fetch = Column(DateTime(timezone=True))
    last_error = Column(Text)
    error_count = Column(Integer, default=0)
    
    # Rate limiting
    requests_per_minute = Column(Integer)
    current_usage = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
