"""
Reddit API client for social sentiment data
"""

import asyncio
import aiohttp
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import time
import re

from app.core.config import settings

logger = logging.getLogger(__name__)


class RedditClient:
    """Reddit API client with rate limiting"""
    
    def __init__(self):
        self.client_id = settings.REDDIT_CLIENT_ID
        self.client_secret = settings.REDDIT_CLIENT_SECRET
        self.user_agent = settings.REDDIT_USER_AGENT
        self.rate_limit = settings.REDDIT_RATE_LIMIT  # calls per minute
        self.last_request_time = 0
        self.request_count = 0
        self.access_token = None
        self.token_expires = 0
        self.session = None
        
        # Target subreddits for crypto discussions
        self.crypto_subreddits = [
            'cryptocurrency',
            'bitcoin',
            'ethereum', 
            'altcoin',
            'cryptomarkets',
            'defi',
            'binance',
            'coinbase'
        ]
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        await self._get_access_token()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def _get_access_token(self):
        """Get OAuth2 access token for Reddit API"""
        if not self.client_id or self.client_id == "demo-client-id":
            logger.warning("Reddit API credentials not configured, using demo mode")
            return
        
        try:
            auth = aiohttp.BasicAuth(self.client_id, self.client_secret)
            headers = {'User-Agent': self.user_agent}
            data = {'grant_type': 'client_credentials'}
            
            async with self.session.post(
                'https://www.reddit.com/api/v1/access_token',
                auth=auth,
                headers=headers,
                data=data
            ) as response:
                if response.status == 200:
                    token_data = await response.json()
                    self.access_token = token_data['access_token']
                    self.token_expires = time.time() + token_data['expires_in'] - 60  # 1 min buffer
                    logger.info("Reddit API access token obtained")
                else:
                    logger.error(f"Failed to get Reddit access token: {response.status}")
                    
        except Exception as e:
            logger.error(f"Error getting Reddit access token: {e}")
    
    async def _rate_limit_check(self):
        """Check and enforce rate limiting"""
        current_time = time.time()
        
        # Reset counter every minute
        if current_time - self.last_request_time > 60:
            self.request_count = 0
            self.last_request_time = current_time
        
        # Check if we've hit the rate limit
        if self.request_count >= self.rate_limit:
            sleep_time = 60 - (current_time - self.last_request_time)
            if sleep_time > 0:
                logger.info(f"Reddit rate limit reached, sleeping for {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)
                self.request_count = 0
                self.last_request_time = time.time()
        
        self.request_count += 1
    
    async def _make_request(self, endpoint: str, params: dict = None) -> Optional[dict]:
        """Make rate-limited request to Reddit API"""
        if not self.access_token:
            logger.warning("No Reddit access token available")
            return None
        
        # Check if token needs refresh
        if time.time() >= self.token_expires:
            await self._get_access_token()
        
        await self._rate_limit_check()
        
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'User-Agent': self.user_agent
        }
        
        url = f"https://oauth.reddit.com{endpoint}"
        
        try:
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    logger.warning("Rate limited by Reddit, waiting...")
                    await asyncio.sleep(60)
                    return await self._make_request(endpoint, params)
                else:
                    logger.error(f"Reddit API error: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error making Reddit request to {endpoint}: {e}")
            return None
    
    async def search_mentions(self, symbol: str, hours: int = 24) -> List[Dict]:
        """Search for mentions of a cryptocurrency symbol"""
        if not self.access_token:
            return []
        
        mentions = []
        
        # Search in multiple subreddits
        for subreddit in self.crypto_subreddits:
            try:
                subreddit_mentions = await self._search_subreddit(subreddit, symbol, hours)
                mentions.extend(subreddit_mentions)
                
                # Add small delay between subreddit searches
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error searching {subreddit} for {symbol}: {e}")
                continue
        
        # Remove duplicates and sort by score
        unique_mentions = self._deduplicate_mentions(mentions)
        return sorted(unique_mentions, key=lambda x: x.get('score', 0), reverse=True)
    
    async def _search_subreddit(self, subreddit: str, symbol: str, hours: int) -> List[Dict]:
        """Search for symbol mentions in a specific subreddit"""
        params = {
            'q': f'{symbol} OR ${symbol}',
            'restrict_sr': 'true',
            'sort': 'new',
            'limit': 25,
            't': 'day' if hours <= 24 else 'week'
        }
        
        endpoint = f'/r/{subreddit}/search'
        data = await self._make_request(endpoint, params)
        
        if not data or 'data' not in data:
            return []
        
        mentions = []
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        for post in data['data']['children']:
            post_data = post['data']
            
            # Convert Reddit timestamp to datetime
            post_time = datetime.fromtimestamp(post_data['created_utc'])
            
            if post_time < cutoff_time:
                continue
            
            # Extract relevant information
            mention = {
                'platform': 'reddit',
                'subreddit': subreddit,
                'title': post_data['title'],
                'content': post_data.get('selftext', ''),
                'author': post_data['author'],
                'score': post_data['score'],
                'num_comments': post_data['num_comments'],
                'url': f"https://reddit.com{post_data['permalink']}",
                'timestamp': post_time,
                'symbol': symbol.upper(),
                'engagement_score': self._calculate_engagement_score(post_data),
                'sentiment_score': self._analyze_sentiment(post_data['title'], post_data.get('selftext', ''))
            }
            
            mentions.append(mention)
        
        return mentions
    
    def _calculate_engagement_score(self, post_data: Dict) -> float:
        """Calculate engagement score based on upvotes and comments"""
        score = post_data.get('score', 0)
        comments = post_data.get('num_comments', 0)
        
        # Weighted score: upvotes + comments * 2 (comments indicate more engagement)
        engagement = score + (comments * 2)
        
        # Normalize to 0-100 scale (logarithmic to handle viral posts)
        import math
        if engagement <= 0:
            return 0.0
        
        return min(100.0, math.log10(engagement + 1) * 25)
    
    def _analyze_sentiment(self, title: str, content: str) -> float:
        """Simple sentiment analysis based on keywords"""
        text = f"{title} {content}".lower()
        
        # Positive keywords
        positive_words = [
            'bullish', 'moon', 'pump', 'buy', 'hodl', 'diamond', 'hands',
            'rocket', 'lambo', 'gains', 'profit', 'up', 'rise', 'surge',
            'breakout', 'rally', 'bull', 'green', 'positive', 'good'
        ]
        
        # Negative keywords
        negative_words = [
            'bearish', 'dump', 'sell', 'crash', 'down', 'fall', 'drop',
            'bear', 'red', 'negative', 'bad', 'loss', 'panic', 'fear',
            'correction', 'dip', 'decline', 'plummet', 'collapse'
        ]
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.0
        
        # Calculate sentiment score (-1 to 1)
        sentiment = (positive_count - negative_count) / max(1, total_words / 10)
        return max(-1.0, min(1.0, sentiment))
    
    def _deduplicate_mentions(self, mentions: List[Dict]) -> List[Dict]:
        """Remove duplicate mentions based on URL"""
        seen_urls = set()
        unique_mentions = []
        
        for mention in mentions:
            url = mention.get('url', '')
            if url not in seen_urls:
                seen_urls.add(url)
                unique_mentions.append(mention)
        
        return unique_mentions
    
    async def get_trending_posts(self, subreddit: str = 'cryptocurrency', limit: int = 10) -> List[Dict]:
        """Get trending posts from a subreddit"""
        if not self.access_token:
            return []
        
        params = {
            'limit': limit,
            't': 'day'
        }
        
        endpoint = f'/r/{subreddit}/hot'
        data = await self._make_request(endpoint, params)
        
        if not data or 'data' not in data:
            return []
        
        trending_posts = []
        for post in data['data']['children']:
            post_data = post['data']
            
            trending_posts.append({
                'title': post_data['title'],
                'score': post_data['score'],
                'num_comments': post_data['num_comments'],
                'author': post_data['author'],
                'subreddit': subreddit,
                'url': f"https://reddit.com{post_data['permalink']}",
                'timestamp': datetime.fromtimestamp(post_data['created_utc']),
                'engagement_score': self._calculate_engagement_score(post_data)
            })
        
        return trending_posts
