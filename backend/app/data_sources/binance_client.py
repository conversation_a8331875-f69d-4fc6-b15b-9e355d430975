"""
Binance WebSocket client for real-time market data
"""

import asyncio
import json
import logging
import websockets
from typing import Dict, List, Callable, Optional
from datetime import datetime
import aiohttp

from app.core.config import settings

logger = logging.getLogger(__name__)


class BinanceWebSocketClient:
    """Binance WebSocket client for real-time price and volume data"""
    
    def __init__(self):
        self.ws_url = "wss://stream.binance.com:9443/ws"
        self.connection = None
        self.is_connected = False
        self.callbacks: Dict[str, List[Callable]] = {}
        self.subscribed_symbols = set()
        
    async def connect(self):
        """Connect to Binance WebSocket"""
        try:
            self.connection = await websockets.connect(self.ws_url)
            self.is_connected = True
            logger.info("Connected to Binance WebSocket")
            
            # Start listening for messages
            asyncio.create_task(self._listen())
            
        except Exception as e:
            logger.error(f"Failed to connect to Binance WebSocket: {e}")
            self.is_connected = False
            raise
    
    async def disconnect(self):
        """Disconnect from WebSocket"""
        if self.connection:
            await self.connection.close()
            self.is_connected = False
            logger.info("Disconnected from Binance WebSocket")
    
    async def subscribe_ticker(self, symbols: List[str], callback: Callable):
        """Subscribe to ticker data for multiple symbols"""
        if not self.is_connected:
            await self.connect()

        # Convert symbols to Binance format (lowercase with USDT)
        binance_symbols = [symbol.lower() + "usdt@ticker" for symbol in symbols]
        
        # Subscribe message
        subscribe_msg = {
            "method": "SUBSCRIBE",
            "params": binance_symbols,
            "id": 1
        }
        
        await self.connection.send(json.dumps(subscribe_msg))
        
        # Register callback
        for symbol in symbols:
            if symbol not in self.callbacks:
                self.callbacks[symbol] = []
            self.callbacks[symbol].append(callback)
            self.subscribed_symbols.add(symbol)
        
        logger.info(f"Subscribed to ticker data for {len(symbols)} symbols")
    
    async def _listen(self):
        """Listen for incoming WebSocket messages"""
        try:
            async for message in self.connection:
                try:
                    data = json.loads(message)
                    await self._handle_message(data)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to decode message: {message}")
                except Exception as e:
                    logger.error(f"Error handling message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed")
            self.is_connected = False
        except Exception as e:
            logger.error(f"Error in WebSocket listener: {e}")
            self.is_connected = False
    
    async def _handle_message(self, data: dict):
        """Handle incoming WebSocket message"""
        logger.debug(f"Received WebSocket message: {data}")

        if 'stream' in data and 'data' in data:
            stream = data['stream']
            ticker_data = data['data']

            # Extract symbol from stream name (e.g., "btcusdt@ticker" -> "BTC")
            if '@ticker' in stream:
                symbol = stream.split('@')[0].upper()
                if 'USDT' in symbol:
                    symbol = symbol.replace('USDT', '')

                # Process ticker data
                processed_data = {
                    'symbol': symbol,
                    'price': float(ticker_data.get('c', 0)),  # Current price
                    'volume': float(ticker_data.get('v', 0)),  # 24h volume
                    'price_change_percent': float(ticker_data.get('P', 0)),  # 24h change %
                    'timestamp': datetime.now(),
                    'source': 'binance_ws'
                }

                logger.debug(f"Processed ticker data for {symbol}: price={processed_data['price']}")

                # Call registered callbacks
                if symbol in self.callbacks:
                    for callback in self.callbacks[symbol]:
                        try:
                            await callback(processed_data)
                        except Exception as e:
                            logger.error(f"Error in callback for {symbol}: {e}")
                else:
                    logger.warning(f"No callbacks registered for symbol {symbol}")
        else:
            logger.debug(f"Received non-ticker message: {data}")


class BinanceRestClient:
    """Binance REST API client for historical data and symbol info"""
    
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_exchange_info(self) -> dict:
        """Get exchange information including all trading symbols"""
        async with self.session.get(f"{self.base_url}/exchangeInfo") as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"Failed to get exchange info: {response.status}")
    
    async def get_top_symbols(self, limit: int = 50) -> List[str]:
        """Get top trading symbols by volume"""
        async with self.session.get(f"{self.base_url}/ticker/24hr") as response:
            if response.status == 200:
                tickers = await response.json()
                
                # Filter USDT pairs and sort by volume
                usdt_pairs = [
                    ticker for ticker in tickers 
                    if ticker['symbol'].endswith('USDT')
                ]
                
                # Sort by volume (descending)
                usdt_pairs.sort(key=lambda x: float(x['quoteVolume']), reverse=True)
                
                # Extract base symbols (remove USDT)
                symbols = [
                    ticker['symbol'].replace('USDT', '') 
                    for ticker in usdt_pairs[:limit]
                ]
                
                return symbols
            else:
                raise Exception(f"Failed to get ticker data: {response.status}")
    
    async def get_klines(self, symbol: str, interval: str = "1h", limit: int = 100) -> List[dict]:
        """Get historical kline/candlestick data"""
        params = {
            'symbol': f"{symbol}USDT",
            'interval': interval,
            'limit': limit
        }
        
        async with self.session.get(f"{self.base_url}/klines", params=params) as response:
            if response.status == 200:
                klines = await response.json()
                
                # Convert to structured format
                processed_klines = []
                for kline in klines:
                    processed_klines.append({
                        'symbol': symbol,
                        'timestamp': datetime.fromtimestamp(kline[0] / 1000),
                        'open': float(kline[1]),
                        'high': float(kline[2]),
                        'low': float(kline[3]),
                        'close': float(kline[4]),
                        'volume': float(kline[5]),
                        'source': 'binance_rest'
                    })
                
                return processed_klines
            else:
                raise Exception(f"Failed to get klines for {symbol}: {response.status}")
