"""
CoinGecko API client for market data and new listings
"""

import asyncio
import aiohttp
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import time

from app.core.config import settings

logger = logging.getLogger(__name__)


class CoinGeckoClient:
    """CoinGecko API client with rate limiting"""
    
    def __init__(self):
        self.base_url = "https://api.coingecko.com/api/v3"
        self.api_key = settings.COINGECKO_API_KEY
        self.rate_limit = settings.COINGECKO_RATE_LIMIT  # calls per minute
        self.last_request_time = 0
        self.request_count = 0
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def _rate_limit_check(self):
        """Check and enforce rate limiting"""
        current_time = time.time()
        
        # Reset counter every minute
        if current_time - self.last_request_time > 60:
            self.request_count = 0
            self.last_request_time = current_time
        
        # Check if we've hit the rate limit
        if self.request_count >= self.rate_limit:
            sleep_time = 60 - (current_time - self.last_request_time)
            if sleep_time > 0:
                logger.info(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)
                self.request_count = 0
                self.last_request_time = time.time()
        
        self.request_count += 1
    
    async def _make_request(self, endpoint: str, params: dict = None) -> dict:
        """Make rate-limited request to CoinGecko API"""
        await self._rate_limit_check()
        
        headers = {}
        if self.api_key and self.api_key != "demo-api-key-for-development":
            headers["x-cg-demo-api-key"] = self.api_key
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    logger.warning("Rate limited by CoinGecko, waiting...")
                    await asyncio.sleep(60)
                    return await self._make_request(endpoint, params)
                else:
                    logger.error(f"CoinGecko API error: {response.status}")
                    response.raise_for_status()
        except Exception as e:
            logger.error(f"Error making request to {endpoint}: {e}")
            raise
    
    async def get_top_coins(self, limit: int = 50) -> List[dict]:
        """Get top cryptocurrencies by market cap"""
        params = {
            'vs_currency': 'usd',
            'order': 'market_cap_desc',
            'per_page': limit,
            'page': 1,
            'sparkline': 'false',
            'price_change_percentage': '1h,24h,7d'
        }
        
        data = await self._make_request("coins/markets", params)
        
        # Process and standardize the data
        processed_coins = []
        for coin in data:
            processed_coins.append({
                'symbol': coin['symbol'].upper(),
                'name': coin['name'],
                'price': coin['current_price'],
                'market_cap': coin['market_cap'],
                'volume_24h': coin['total_volume'],
                'price_change_1h': coin.get('price_change_percentage_1h_in_currency'),
                'price_change_24h': coin.get('price_change_percentage_24h_in_currency'),
                'price_change_7d': coin.get('price_change_percentage_7d_in_currency'),
                'timestamp': datetime.now(),
                'source': 'coingecko'
            })
        
        return processed_coins
    
    async def get_coin_data(self, coin_id: str) -> dict:
        """Get detailed data for a specific coin"""
        params = {
            'localization': 'false',
            'tickers': 'false',
            'market_data': 'true',
            'community_data': 'false',
            'developer_data': 'false',
            'sparkline': 'false'
        }
        
        data = await self._make_request(f"coins/{coin_id}", params)
        
        market_data = data.get('market_data', {})
        
        return {
            'symbol': data['symbol'].upper(),
            'name': data['name'],
            'price': market_data.get('current_price', {}).get('usd'),
            'market_cap': market_data.get('market_cap', {}).get('usd'),
            'volume_24h': market_data.get('total_volume', {}).get('usd'),
            'price_change_24h': market_data.get('price_change_percentage_24h'),
            'all_time_high': market_data.get('ath', {}).get('usd'),
            'all_time_low': market_data.get('atl', {}).get('usd'),
            'timestamp': datetime.now(),
            'source': 'coingecko'
        }
    
    async def get_new_listings(self, days: int = 7) -> List[dict]:
        """Get recently listed coins"""
        # CoinGecko doesn't have a direct "new listings" endpoint
        # We'll get recently added coins by checking coins added in the last week
        
        # Get all coins list
        all_coins = await self._make_request("coins/list")
        
        # For demo purposes, we'll return the last 20 coins from the list
        # In a real implementation, you'd need to track when coins were added
        recent_coins = all_coins[-20:] if len(all_coins) > 20 else all_coins
        
        new_listings = []
        for coin in recent_coins:
            try:
                # Get market data for each coin
                coin_data = await self.get_coin_data(coin['id'])
                new_listings.append({
                    'symbol': coin_data['symbol'],
                    'name': coin_data['name'],
                    'price': coin_data['price'],
                    'market_cap': coin_data['market_cap'],
                    'listing_date': datetime.now() - timedelta(days=days),
                    'source': 'coingecko'
                })
                
                # Add small delay to avoid overwhelming the API
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.warning(f"Failed to get data for {coin['id']}: {e}")
                continue
        
        return new_listings
    
    async def search_coins(self, query: str) -> List[dict]:
        """Search for coins by name or symbol"""
        params = {'query': query}
        
        data = await self._make_request("search", params)
        
        coins = data.get('coins', [])
        
        return [
            {
                'id': coin['id'],
                'symbol': coin['symbol'].upper(),
                'name': coin['name'],
                'market_cap_rank': coin.get('market_cap_rank'),
                'source': 'coingecko'
            }
            for coin in coins[:10]  # Limit to top 10 results
        ]
    
    async def get_trending_coins(self) -> List[dict]:
        """Get trending coins (most searched)"""
        data = await self._make_request("search/trending")
        
        trending_coins = []
        for item in data.get('coins', []):
            coin = item.get('item', {})
            trending_coins.append({
                'symbol': coin.get('symbol', '').upper(),
                'name': coin.get('name', ''),
                'market_cap_rank': coin.get('market_cap_rank'),
                'trending_rank': len(trending_coins) + 1,
                'timestamp': datetime.now(),
                'source': 'coingecko'
            })
        
        return trending_coins
