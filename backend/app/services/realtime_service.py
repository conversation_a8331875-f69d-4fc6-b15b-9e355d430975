"""
Real-time Broadcasting Service

Handles real-time data streaming and pattern alert broadcasting
through WebSocket connections.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from app.websocket.manager import connection_manager
from app.services.data_storage import data_storage
from app.services.pattern_service import pattern_service

logger = logging.getLogger(__name__)


class RealtimeService:
    """Service for real-time data broadcasting"""
    
    def __init__(self):
        self.is_running = False
        self.broadcast_tasks: List[asyncio.Task] = []
        
        # Broadcasting intervals (seconds)
        self.market_data_interval = 30  # Broadcast market data every 30 seconds
        self.pattern_check_interval = 60  # Check for new patterns every minute
        self.status_update_interval = 120  # Broadcast status every 2 minutes
        
        # Last broadcast timestamps
        self.last_market_broadcast = {}
        self.last_pattern_check = datetime.min
        self.last_status_broadcast = datetime.min
    
    async def start_broadcasting(self):
        """Start real-time broadcasting services"""
        if self.is_running:
            logger.warning("Real-time broadcasting is already running")
            return
        
        self.is_running = True
        logger.info("Starting real-time broadcasting service")
        
        # Start WebSocket manager background tasks
        await connection_manager.start_background_tasks()
        
        # Start broadcasting tasks
        self.broadcast_tasks = [
            asyncio.create_task(self._market_data_broadcaster()),
            asyncio.create_task(self._pattern_alert_broadcaster()),
            asyncio.create_task(self._status_broadcaster())
        ]
        
        logger.info("Real-time broadcasting tasks started")
    
    async def stop_broadcasting(self):
        """Stop real-time broadcasting services"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("Stopping real-time broadcasting service")
        
        # Cancel all broadcasting tasks
        for task in self.broadcast_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.broadcast_tasks:
            await asyncio.gather(*self.broadcast_tasks, return_exceptions=True)
        
        self.broadcast_tasks.clear()
        
        # Stop WebSocket manager background tasks
        await connection_manager.stop_background_tasks()
        
        logger.info("Real-time broadcasting stopped")
    
    async def _market_data_broadcaster(self):
        """Background task to broadcast market data updates"""
        while self.is_running:
            try:
                await asyncio.sleep(self.market_data_interval)
                
                if not connection_manager.active_connections:
                    continue  # No clients connected
                
                # Get symbols with recent data
                symbols = await data_storage.get_symbols_with_data()
                
                # Broadcast data for top 10 symbols to avoid overwhelming clients
                top_symbols = symbols[:10]
                
                for symbol in top_symbols:
                    try:
                        # Get latest market data
                        market_data = await data_storage.get_recent_market_data(symbol, hours=0.1)  # Last 6 minutes
                        
                        if market_data:
                            latest_data = market_data[0]  # Most recent
                            
                            # Check if this is new data (avoid duplicate broadcasts)
                            last_broadcast_time = self.last_market_broadcast.get(symbol, datetime.min)
                            data_timestamp = latest_data['timestamp']
                            
                            if isinstance(data_timestamp, str):
                                data_timestamp = datetime.fromisoformat(data_timestamp.replace('Z', '+00:00'))
                            
                            if data_timestamp > last_broadcast_time:
                                # Broadcast new market data
                                await connection_manager.broadcast_market_data(symbol, latest_data)
                                self.last_market_broadcast[symbol] = data_timestamp
                                
                                logger.debug(f"Broadcasted market data for {symbol}: ${latest_data['price']}")
                    
                    except Exception as e:
                        logger.error(f"Error broadcasting market data for {symbol}: {e}")
                        continue
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in market data broadcaster: {e}")
    
    async def _pattern_alert_broadcaster(self):
        """Background task to broadcast pattern detection alerts"""
        while self.is_running:
            try:
                await asyncio.sleep(self.pattern_check_interval)
                
                if not connection_manager.active_connections:
                    continue  # No clients connected
                
                # Check for new patterns since last check
                current_time = datetime.now()
                time_window = current_time - self.last_pattern_check
                
                # Get recent patterns (last check + 1 minute buffer)
                hours_to_check = max(0.1, time_window.total_seconds() / 3600 + 0.02)  # Add 1 minute buffer
                recent_patterns = await pattern_service.get_recent_patterns(hours=hours_to_check)
                
                # Filter patterns that are newer than last check
                new_patterns = []
                for pattern in recent_patterns:
                    pattern_time = datetime.fromisoformat(pattern['trigger_time'].replace('Z', '+00:00'))
                    if pattern_time > self.last_pattern_check:
                        new_patterns.append(pattern)
                
                # Broadcast new patterns
                for pattern in new_patterns:
                    await connection_manager.broadcast_pattern_alert(pattern)
                    logger.info(f"Broadcasted pattern alert: {pattern['pattern_type']} for {pattern['symbol']}")
                
                self.last_pattern_check = current_time
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in pattern alert broadcaster: {e}")
    
    async def _status_broadcaster(self):
        """Background task to broadcast system status updates"""
        while self.is_running:
            try:
                await asyncio.sleep(self.status_update_interval)
                
                if not connection_manager.active_connections:
                    continue  # No clients connected
                
                # Get current system status
                status = await self._get_system_status()
                
                # Broadcast status update
                await connection_manager.broadcast_system_status(status)
                
                self.last_status_broadcast = datetime.now()
                logger.debug("Broadcasted system status update")
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in status broadcaster: {e}")
    
    async def _get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            from app.services.data_collector import data_collector
            
            # Get data collection status
            symbols_with_data = await data_storage.get_symbols_with_data()
            
            # Get pattern detection status
            pattern_status = await pattern_service.get_detection_status()
            
            # Get recent patterns count
            recent_patterns = await pattern_service.get_recent_patterns(hours=1)
            
            return {
                'data_collection': {
                    'is_running': data_collector.is_running,
                    'symbols_monitored': len(data_collector.monitored_symbols),
                    'symbols_with_data': len(symbols_with_data),
                    'binance_ws_connected': getattr(data_collector, 'binance_ws_connected', False)
                },
                'pattern_detection': {
                    'is_running': pattern_status.get('is_running', False),
                    'detectors_count': len(pattern_status.get('registered_detectors', [])),
                    'patterns_last_hour': len(recent_patterns),
                    'confidence_threshold': pattern_status.get('min_confidence_threshold', 0.7)
                },
                'websocket': {
                    'active_connections': len(connection_manager.active_connections),
                    'broadcasting_active': self.is_running
                },
                'timestamp': datetime.now().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def broadcast_immediate_alert(self, pattern: Dict[str, Any]):
        """Broadcast an immediate pattern alert (called when pattern is detected)"""
        if connection_manager.active_connections:
            await connection_manager.broadcast_pattern_alert(pattern)
            logger.info(f"Immediate alert broadcasted: {pattern.get('pattern_type')} for {pattern.get('symbol')}")
    
    async def broadcast_market_update(self, symbol: str, data: Dict[str, Any]):
        """Broadcast an immediate market data update"""
        if connection_manager.active_connections:
            await connection_manager.broadcast_market_data(symbol, data)
            logger.debug(f"Immediate market update broadcasted for {symbol}")
    
    def get_broadcasting_status(self) -> Dict[str, Any]:
        """Get real-time broadcasting service status"""
        return {
            'is_running': self.is_running,
            'active_tasks': len(self.broadcast_tasks),
            'market_data_interval': self.market_data_interval,
            'pattern_check_interval': self.pattern_check_interval,
            'status_update_interval': self.status_update_interval,
            'active_connections': len(connection_manager.active_connections),
            'last_status_broadcast': self.last_status_broadcast.isoformat() if self.last_status_broadcast != datetime.min else None
        }


# Global real-time service instance
realtime_service = RealtimeService()
