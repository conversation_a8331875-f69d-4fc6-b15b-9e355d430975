"""
Data storage service for market data and events
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert
from sqlalchemy.dialects.postgresql import insert as pg_insert

from app.core.database import AsyncSessionLocal
from app.models.market_data import MarketData, Event, SocialMention
from app.models.correlations import Correlation, DataSource

logger = logging.getLogger(__name__)


class DataStorageService:
    """Service for storing market data and events"""
    
    def __init__(self):
        self.batch_size = 1000
        self.market_data_buffer = []
        self.events_buffer = []
    
    async def store_market_data(self, data: Dict) -> bool:
        """Store market data point"""
        try:
            async with AsyncSessionLocal() as session:
                market_data = MarketData(
                    time=data['timestamp'],
                    symbol=data['symbol'],
                    price=data['price'],
                    volume=data.get('volume'),
                    market_cap=data.get('market_cap'),
                    source=data['source']
                )
                
                # Simple insert for now (we'll handle duplicates later)
                session.add(market_data)
                
                await session.execute(stmt)
                await session.commit()
                
                logger.debug(f"Stored market data for {data['symbol']} at {data['timestamp']}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to store market data: {e}")
            return False
    
    async def store_market_data_batch(self, data_list: List[Dict]) -> int:
        """Store multiple market data points efficiently"""
        if not data_list:
            return 0
        
        try:
            async with AsyncSessionLocal() as session:
                # Prepare batch insert data
                insert_data = []
                for data in data_list:
                    insert_data.append({
                        'time': data['timestamp'],
                        'symbol': data['symbol'],
                        'price': data['price'],
                        'volume': data.get('volume'),
                        'market_cap': data.get('market_cap'),
                        'source': data['source']
                    })
                
                # Batch insert
                stmt = pg_insert(MarketData).values(insert_data)
                
                await session.execute(stmt)
                await session.commit()
                
                logger.info(f"Stored batch of {len(data_list)} market data points")
                return len(data_list)
                
        except Exception as e:
            logger.error(f"Failed to store market data batch: {e}")
            return 0
    
    async def store_event(self, event_data: Dict) -> bool:
        """Store an event"""
        try:
            async with AsyncSessionLocal() as session:
                event = Event(
                    time=event_data['timestamp'],
                    event_type=event_data['event_type'],
                    symbol=event_data.get('symbol'),
                    data=event_data.get('data'),
                    source=event_data['source'],
                    confidence_score=event_data.get('confidence_score')
                )
                
                session.add(event)
                await session.commit()
                
                logger.debug(f"Stored event: {event_data['event_type']} for {event_data.get('symbol')}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to store event: {e}")
            return False
    
    async def store_social_mention(self, mention_data: Dict) -> bool:
        """Store social media mention"""
        try:
            async with AsyncSessionLocal() as session:
                mention = SocialMention(
                    time=mention_data['timestamp'],
                    symbol=mention_data.get('symbol'),
                    platform=mention_data['platform'],
                    account=mention_data.get('account'),
                    content=mention_data.get('content'),
                    engagement_score=mention_data.get('engagement_score'),
                    sentiment_score=mention_data.get('sentiment_score'),
                    source_url=mention_data.get('source_url')
                )
                
                session.add(mention)
                await session.commit()
                
                logger.debug(f"Stored social mention from {mention_data['platform']}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to store social mention: {e}")
            return False
    
    async def get_recent_market_data(self, symbol: str, hours: int = 24) -> List[Dict]:
        """Get recent market data for a symbol"""
        try:
            async with AsyncSessionLocal() as session:
                # Calculate time threshold
                time_threshold = datetime.now() - timedelta(hours=hours)
                
                stmt = select(MarketData).where(
                    MarketData.symbol == symbol,
                    MarketData.time >= time_threshold
                ).order_by(MarketData.time.desc())
                
                result = await session.execute(stmt)
                market_data = result.scalars().all()
                
                return [
                    {
                        'timestamp': md.time,
                        'symbol': md.symbol,
                        'price': float(md.price) if md.price else None,
                        'volume': float(md.volume) if md.volume else None,
                        'market_cap': float(md.market_cap) if md.market_cap else None,
                        'source': md.source
                    }
                    for md in market_data
                ]
                
        except Exception as e:
            logger.error(f"Failed to get recent market data for {symbol}: {e}")
            return []
    
    async def get_symbols_with_data(self) -> List[str]:
        """Get list of symbols that have market data"""
        try:
            async with AsyncSessionLocal() as session:
                stmt = select(MarketData.symbol).distinct()
                result = await session.execute(stmt)
                symbols = result.scalars().all()
                
                return list(symbols)
                
        except Exception as e:
            logger.error(f"Failed to get symbols with data: {e}")
            return []
    
    async def update_data_source_health(self, source_name: str, success: bool, error_msg: str = None):
        """Update data source health status"""
        try:
            async with AsyncSessionLocal() as session:
                # Try to find existing data source
                stmt = select(DataSource).where(DataSource.name == source_name)
                result = await session.execute(stmt)
                data_source = result.scalar_one_or_none()
                
                if not data_source:
                    # Create new data source record
                    data_source = DataSource(
                        name=source_name,
                        source_type='api',  # Default type
                        is_active=True,
                        error_count=0
                    )
                    session.add(data_source)
                
                # Update health status
                if success:
                    data_source.last_successful_fetch = datetime.now()
                    data_source.error_count = 0
                    data_source.last_error = None
                    data_source.is_active = True
                else:
                    data_source.error_count += 1
                    data_source.last_error = error_msg
                    # Deactivate if too many errors
                    if data_source.error_count >= 5:
                        data_source.is_active = False
                
                await session.commit()
                
        except Exception as e:
            logger.error(f"Failed to update data source health for {source_name}: {e}")


# Global instance
data_storage = DataStorageService()
