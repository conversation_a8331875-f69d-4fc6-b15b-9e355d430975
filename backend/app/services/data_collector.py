"""
Data collection service that orchestrates data gathering from multiple sources
"""

import asyncio
import logging
from typing import List, Dict
from datetime import datetime

from app.data_sources.binance_client import BinanceWebSocketClient, BinanceRestClient
from app.data_sources.coingecko_client import CoinGeckoClient
from app.services.data_storage import data_storage
from app.core.config import settings

logger = logging.getLogger(__name__)


class DataCollectionService:
    """Orchestrates data collection from multiple sources"""
    
    def __init__(self):
        self.binance_ws = BinanceWebSocketClient()
        self.is_running = False
        self.monitored_symbols = []
        self.collection_tasks = []
    
    async def start_collection(self):
        """Start data collection from all sources"""
        if self.is_running:
            logger.warning("Data collection is already running")
            return
        
        self.is_running = True
        logger.info("Starting data collection service")
        
        try:
            # Get list of symbols to monitor
            await self._initialize_symbols()
            
            # Start collection tasks
            self.collection_tasks = [
                asyncio.create_task(self._collect_binance_data()),
                asyncio.create_task(self._collect_coingecko_data()),
                asyncio.create_task(self._health_check_loop())
            ]
            
            # Wait for all tasks
            await asyncio.gather(*self.collection_tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"Error in data collection service: {e}")
        finally:
            self.is_running = False
    
    async def stop_collection(self):
        """Stop data collection"""
        logger.info("Stopping data collection service")
        self.is_running = False
        
        # Cancel all tasks
        for task in self.collection_tasks:
            if not task.done():
                task.cancel()
        
        # Disconnect WebSocket
        if self.binance_ws.is_connected:
            await self.binance_ws.disconnect()
    
    async def _initialize_symbols(self):
        """Initialize the list of symbols to monitor"""
        try:
            # Get top symbols from Binance
            async with BinanceRestClient() as binance_rest:
                top_symbols = await binance_rest.get_top_symbols(
                    limit=settings.MAX_SYMBOLS_TO_MONITOR
                )
                self.monitored_symbols = top_symbols
                logger.info(f"Monitoring {len(self.monitored_symbols)} symbols: {self.monitored_symbols[:10]}...")
                
        except Exception as e:
            logger.error(f"Failed to initialize symbols: {e}")
            # Fallback to hardcoded list
            self.monitored_symbols = [
                'BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'XRP', 'DOT', 'DOGE', 'AVAX', 'MATIC'
            ]
            logger.info(f"Using fallback symbols: {self.monitored_symbols}")
    
    async def _collect_binance_data(self):
        """Collect real-time data from Binance WebSocket"""
        logger.info("Starting Binance WebSocket data collection")
        
        while self.is_running:
            try:
                # Connect to WebSocket
                await self.binance_ws.connect()
                
                # Subscribe to ticker data
                await self.binance_ws.subscribe_ticker(
                    self.monitored_symbols,
                    self._handle_binance_ticker
                )
                
                # Keep connection alive
                while self.is_running and self.binance_ws.is_connected:
                    await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in Binance data collection: {e}")
                await data_storage.update_data_source_health('binance_ws', False, str(e))
                
                # Wait before reconnecting
                if self.is_running:
                    await asyncio.sleep(settings.BINANCE_RECONNECT_DELAY)
    
    async def _handle_binance_ticker(self, ticker_data: Dict):
        """Handle incoming ticker data from Binance"""
        try:
            # Store the data
            success = await data_storage.store_market_data(ticker_data)
            
            if success:
                await data_storage.update_data_source_health('binance_ws', True)
            else:
                await data_storage.update_data_source_health('binance_ws', False, "Failed to store data")
                
        except Exception as e:
            logger.error(f"Error handling Binance ticker data: {e}")
            await data_storage.update_data_source_health('binance_ws', False, str(e))
    
    async def _collect_coingecko_data(self):
        """Collect data from CoinGecko API periodically"""
        logger.info("Starting CoinGecko data collection")
        
        while self.is_running:
            try:
                async with CoinGeckoClient() as coingecko:
                    # Get top coins data
                    top_coins = await coingecko.get_top_coins(
                        limit=settings.MAX_SYMBOLS_TO_MONITOR
                    )
                    
                    # Store the data
                    stored_count = await data_storage.store_market_data_batch(top_coins)
                    
                    if stored_count > 0:
                        await data_storage.update_data_source_health('coingecko', True)
                        logger.info(f"Stored {stored_count} CoinGecko data points")
                    else:
                        await data_storage.update_data_source_health('coingecko', False, "No data stored")
                
                # Wait before next collection (CoinGecko rate limits)
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Error in CoinGecko data collection: {e}")
                await data_storage.update_data_source_health('coingecko', False, str(e))
                
                # Wait before retrying
                if self.is_running:
                    await asyncio.sleep(60)  # 1 minute
    
    async def _health_check_loop(self):
        """Periodic health check and monitoring"""
        logger.info("Starting health check loop")
        
        while self.is_running:
            try:
                # Check WebSocket connection
                if not self.binance_ws.is_connected:
                    logger.warning("Binance WebSocket disconnected")
                
                # Log collection status
                symbols_with_data = await data_storage.get_symbols_with_data()
                logger.info(f"Currently tracking {len(symbols_with_data)} symbols with data")
                
                # Wait before next check
                await asyncio.sleep(60)  # 1 minute
                
            except Exception as e:
                logger.error(f"Error in health check: {e}")
                await asyncio.sleep(60)
    
    async def get_collection_status(self) -> Dict:
        """Get current collection status"""
        symbols_with_data = await data_storage.get_symbols_with_data()
        
        return {
            'is_running': self.is_running,
            'monitored_symbols_count': len(self.monitored_symbols),
            'symbols_with_data_count': len(symbols_with_data),
            'binance_ws_connected': self.binance_ws.is_connected,
            'monitored_symbols': self.monitored_symbols[:10],  # First 10 for display
            'symbols_with_data': symbols_with_data[:10]  # First 10 for display
        }
    
    async def add_symbol(self, symbol: str):
        """Add a symbol to monitoring"""
        if symbol not in self.monitored_symbols:
            self.monitored_symbols.append(symbol)
            
            # If WebSocket is connected, subscribe to the new symbol
            if self.binance_ws.is_connected:
                await self.binance_ws.subscribe_ticker([symbol], self._handle_binance_ticker)
            
            logger.info(f"Added {symbol} to monitoring")
    
    async def remove_symbol(self, symbol: str):
        """Remove a symbol from monitoring"""
        if symbol in self.monitored_symbols:
            self.monitored_symbols.remove(symbol)
            logger.info(f"Removed {symbol} from monitoring")


# Global instance
data_collector = DataCollectionService()
