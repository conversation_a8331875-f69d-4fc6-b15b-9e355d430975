"""
Pattern Detection Service

Orchestrates pattern detection across all monitored symbols and manages
the storage and retrieval of detected patterns.
"""

import asyncio
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta

from app.pattern_detection.base import pattern_engine, PatternMatch
from app.pattern_detection.cex_listing import CEXListingDetector, WhaleAccumulationDetector
from app.services.data_storage import data_storage
from app.models.correlations import Correlation
from app.core.database import AsyncSessionLocal
from app.core.config import settings

logger = logging.getLogger(__name__)


class PatternDetectionService:
    """Service for running pattern detection and managing results"""
    
    def __init__(self):
        self.is_running = False
        self.detection_interval = 300  # 5 minutes
        self.analysis_window = settings.CORRELATION_WINDOW_MINUTES
        self.min_confidence = settings.MIN_CONFIDENCE_THRESHOLD
        
        # Initialize pattern detectors
        self._initialize_detectors()
    
    def _initialize_detectors(self):
        """Initialize and register pattern detectors"""
        try:
            # Register CEX listing detector
            cex_detector = CEXListingDetector(min_confidence=self.min_confidence)
            pattern_engine.register_detector(cex_detector)
            
            # Register whale accumulation detector
            whale_detector = WhaleAccumulationDetector(min_confidence=self.min_confidence)
            pattern_engine.register_detector(whale_detector)
            
            logger.info(f"Initialized {len(pattern_engine.detectors)} pattern detectors")
            
        except Exception as e:
            logger.error(f"Error initializing pattern detectors: {e}")
    
    async def start_detection(self):
        """Start continuous pattern detection"""
        if self.is_running:
            logger.warning("Pattern detection is already running")
            return
        
        self.is_running = True
        logger.info("Starting pattern detection service")
        
        try:
            while self.is_running:
                await self._run_detection_cycle()
                await asyncio.sleep(self.detection_interval)
                
        except Exception as e:
            logger.error(f"Error in pattern detection service: {e}")
        finally:
            self.is_running = False
    
    async def stop_detection(self):
        """Stop pattern detection"""
        logger.info("Stopping pattern detection service")
        self.is_running = False
    
    async def _run_detection_cycle(self):
        """Run one cycle of pattern detection across all symbols"""
        try:
            # Get symbols with recent data
            symbols = await data_storage.get_symbols_with_data()
            
            if not symbols:
                logger.debug("No symbols with data available for pattern detection")
                return
            
            logger.info(f"Running pattern detection for {len(symbols)} symbols")
            
            # Analyze each symbol
            total_patterns = 0
            for symbol in symbols:
                try:
                    patterns = await pattern_engine.analyze_symbol(symbol, self.analysis_window)
                    
                    if patterns:
                        # Store detected patterns
                        stored_count = await self._store_patterns(patterns)
                        total_patterns += stored_count
                        
                        logger.info(f"Detected {len(patterns)} patterns for {symbol}, "
                                  f"stored {stored_count}")
                
                except Exception as e:
                    logger.error(f"Error analyzing symbol {symbol}: {e}")
                    continue
            
            if total_patterns > 0:
                logger.info(f"Detection cycle complete: {total_patterns} patterns detected")
            else:
                logger.debug("Detection cycle complete: no patterns detected")
                
        except Exception as e:
            logger.error(f"Error in detection cycle: {e}")
    
    async def _store_patterns(self, patterns: List[PatternMatch]) -> int:
        """Store detected patterns in database"""
        stored_count = 0
        
        try:
            async with AsyncSessionLocal() as session:
                for pattern in patterns:
                    # Check if similar pattern already exists recently
                    if await self._is_duplicate_pattern(session, pattern):
                        logger.debug(f"Skipping duplicate pattern: {pattern.pattern_type} for {pattern.symbol}")
                        continue
                    
                    # Create correlation record
                    correlation = Correlation(
                        symbol=pattern.symbol,
                        pattern_type=pattern.pattern_type,
                        confidence_score=pattern.confidence,
                        trigger_time=pattern.trigger_time,
                        evidence=pattern.evidence,
                        status='active'
                    )
                    
                    session.add(correlation)
                    stored_count += 1
                
                await session.commit()
                
        except Exception as e:
            logger.error(f"Error storing patterns: {e}")
        
        return stored_count
    
    async def _is_duplicate_pattern(self, session, pattern: PatternMatch) -> bool:
        """Check if a similar pattern was recently detected"""
        from sqlalchemy import select
        
        try:
            # Look for similar patterns in the last hour
            cutoff_time = datetime.now() - timedelta(hours=1)
            
            stmt = select(Correlation).where(
                Correlation.symbol == pattern.symbol,
                Correlation.pattern_type == pattern.pattern_type,
                Correlation.trigger_time >= cutoff_time,
                Correlation.status == 'active'
            )
            
            result = await session.execute(stmt)
            existing = result.scalar_one_or_none()
            
            return existing is not None
            
        except Exception as e:
            logger.error(f"Error checking for duplicate patterns: {e}")
            return False
    
    async def get_recent_patterns(self, hours: int = 24, symbol: Optional[str] = None) -> List[Dict]:
        """Get recently detected patterns"""
        try:
            async with AsyncSessionLocal() as session:
                from sqlalchemy import select
                
                cutoff_time = datetime.now() - timedelta(hours=hours)
                
                stmt = select(Correlation).where(
                    Correlation.trigger_time >= cutoff_time,
                    Correlation.status == 'active'
                ).order_by(Correlation.trigger_time.desc())
                
                if symbol:
                    stmt = stmt.where(Correlation.symbol == symbol)
                
                result = await session.execute(stmt)
                correlations = result.scalars().all()
                
                patterns = []
                for corr in correlations:
                    patterns.append({
                        'id': str(corr.id),
                        'symbol': corr.symbol,
                        'pattern_type': corr.pattern_type,
                        'confidence': float(corr.confidence_score),
                        'trigger_time': corr.trigger_time.isoformat(),
                        'evidence': corr.evidence,
                        'status': corr.status,
                        'created_at': corr.created_at.isoformat()
                    })
                
                return patterns
                
        except Exception as e:
            logger.error(f"Error getting recent patterns: {e}")
            return []
    
    async def analyze_symbol_now(self, symbol: str) -> List[Dict]:
        """Analyze a specific symbol immediately"""
        try:
            patterns = await pattern_engine.analyze_symbol(symbol, self.analysis_window)
            
            # Convert to dict format
            pattern_dicts = []
            for pattern in patterns:
                pattern_dict = pattern.to_dict()
                pattern_dict['trigger_time'] = pattern_dict['trigger_time'].isoformat()
                pattern_dicts.append(pattern_dict)
            
            return pattern_dicts
            
        except Exception as e:
            logger.error(f"Error analyzing symbol {symbol}: {e}")
            return []
    
    async def get_detection_status(self) -> Dict:
        """Get current detection service status"""
        try:
            # Get recent patterns count
            recent_patterns = await self.get_recent_patterns(hours=24)
            patterns_by_type = {}
            for pattern in recent_patterns:
                pattern_type = pattern['pattern_type']
                patterns_by_type[pattern_type] = patterns_by_type.get(pattern_type, 0) + 1
            
            # Get symbols being monitored
            symbols = await data_storage.get_symbols_with_data()
            
            return {
                'is_running': self.is_running,
                'detection_interval_seconds': self.detection_interval,
                'analysis_window_minutes': self.analysis_window,
                'min_confidence_threshold': self.min_confidence,
                'registered_detectors': pattern_engine.get_registered_detectors(),
                'symbols_monitored': len(symbols),
                'patterns_last_24h': len(recent_patterns),
                'patterns_by_type': patterns_by_type,
                'last_cycle_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting detection status: {e}")
            return {
                'is_running': self.is_running,
                'error': str(e)
            }
    
    async def update_confidence_threshold(self, new_threshold: float):
        """Update minimum confidence threshold"""
        if 0.0 <= new_threshold <= 1.0:
            self.min_confidence = new_threshold
            # Update all detectors
            for detector in pattern_engine.detectors:
                detector.min_confidence = new_threshold
            logger.info(f"Updated confidence threshold to {new_threshold}")
        else:
            raise ValueError("Confidence threshold must be between 0.0 and 1.0")


# Global pattern detection service instance
pattern_service = PatternDetectionService()
