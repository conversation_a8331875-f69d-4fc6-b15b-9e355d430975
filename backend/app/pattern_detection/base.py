"""
Base classes for pattern detection
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class PatternMatch:
    """Represents a detected pattern"""
    pattern_type: str
    symbol: str
    confidence: float
    trigger_time: datetime
    evidence: Dict[str, Any]
    description: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return {
            'pattern_type': self.pattern_type,
            'symbol': self.symbol,
            'confidence': self.confidence,
            'trigger_time': self.trigger_time,
            'evidence': self.evidence,
            'description': self.description
        }


@dataclass
class CorrelationWindow:
    """Time window for correlation analysis"""
    symbol: str
    start_time: datetime
    end_time: datetime
    market_data: List[Dict[str, Any]]
    events: List[Dict[str, Any]]
    social_mentions: List[Dict[str, Any]]
    
    @property
    def duration_minutes(self) -> int:
        """Get window duration in minutes"""
        return int((self.end_time - self.start_time).total_seconds() / 60)


class PatternDetector(ABC):
    """Base class for all pattern detectors"""
    
    def __init__(self, name: str, min_confidence: float = 0.7):
        self.name = name
        self.min_confidence = min_confidence
        self.logger = logging.getLogger(f"pattern_detector.{name}")
    
    @abstractmethod
    async def detect(self, window: CorrelationWindow) -> Optional[PatternMatch]:
        """
        Detect patterns in the given correlation window
        
        Args:
            window: CorrelationWindow containing market data and events
            
        Returns:
            PatternMatch if pattern detected with sufficient confidence, None otherwise
        """
        pass
    
    @abstractmethod
    def get_pattern_description(self) -> str:
        """Get human-readable description of what this detector looks for"""
        pass
    
    def _calculate_price_change(self, market_data: List[Dict], minutes: int = 60) -> Optional[float]:
        """Calculate price change percentage over specified time period"""
        if len(market_data) < 2:
            return None
        
        # Sort by timestamp
        sorted_data = sorted(market_data, key=lambda x: x['timestamp'])
        
        # Find data points for comparison
        latest = sorted_data[-1]
        cutoff_time = latest['timestamp'] - timedelta(minutes=minutes)
        
        # Find earliest point within the time window
        earliest = None
        for data_point in sorted_data:
            if data_point['timestamp'] >= cutoff_time:
                earliest = data_point
                break
        
        if not earliest or earliest['price'] is None or latest['price'] is None:
            return None
        
        if earliest['price'] == 0:
            return None
        
        return ((latest['price'] - earliest['price']) / earliest['price']) * 100
    
    def _calculate_volume_ratio(self, market_data: List[Dict], minutes: int = 60) -> Optional[float]:
        """Calculate volume ratio compared to average"""
        if len(market_data) < 2:
            return None
        
        # Sort by timestamp
        sorted_data = sorted(market_data, key=lambda x: x['timestamp'])
        
        # Get recent volume (last data point)
        latest = sorted_data[-1]
        if latest['volume'] is None:
            return None
        
        # Calculate average volume from historical data
        historical_volumes = []
        for data_point in sorted_data[:-1]:  # Exclude latest
            if data_point['volume'] is not None:
                historical_volumes.append(data_point['volume'])
        
        if not historical_volumes:
            return None
        
        avg_volume = sum(historical_volumes) / len(historical_volumes)
        if avg_volume == 0:
            return None
        
        return latest['volume'] / avg_volume
    
    def _find_social_mentions(self, social_mentions: List[Dict], keywords: List[str]) -> List[Dict]:
        """Find social mentions containing specific keywords"""
        matches = []
        for mention in social_mentions:
            content = mention.get('content', '').lower()
            if any(keyword.lower() in content for keyword in keywords):
                matches.append(mention)
        return matches
    
    def _calculate_confidence(self, factors: Dict[str, float], weights: Dict[str, float]) -> float:
        """Calculate weighted confidence score"""
        total_weight = sum(weights.values())
        if total_weight == 0:
            return 0.0
        
        weighted_sum = sum(factors.get(factor, 0) * weight for factor, weight in weights.items())
        return min(1.0, weighted_sum / total_weight)


class PatternDetectionEngine:
    """Main engine that orchestrates pattern detection"""
    
    def __init__(self):
        self.detectors: List[PatternDetector] = []
        self.logger = logging.getLogger("pattern_detection_engine")
    
    def register_detector(self, detector: PatternDetector):
        """Register a pattern detector"""
        self.detectors.append(detector)
        self.logger.info(f"Registered pattern detector: {detector.name}")
    
    async def analyze_symbol(self, symbol: str, window_minutes: int = 60) -> List[PatternMatch]:
        """
        Analyze a symbol for patterns within the specified time window
        
        Args:
            symbol: Symbol to analyze
            window_minutes: Time window in minutes to analyze
            
        Returns:
            List of detected patterns
        """
        try:
            # Create correlation window
            window = await self._create_correlation_window(symbol, window_minutes)
            
            # Run all detectors
            patterns = []
            for detector in self.detectors:
                try:
                    pattern = await detector.detect(window)
                    if pattern and pattern.confidence >= detector.min_confidence:
                        patterns.append(pattern)
                        self.logger.info(f"Pattern detected: {pattern.pattern_type} for {symbol} "
                                       f"(confidence: {pattern.confidence:.2f})")
                except Exception as e:
                    self.logger.error(f"Error in detector {detector.name} for {symbol}: {e}")
            
            return patterns
            
        except Exception as e:
            self.logger.error(f"Error analyzing symbol {symbol}: {e}")
            return []
    
    async def _create_correlation_window(self, symbol: str, window_minutes: int) -> CorrelationWindow:
        """Create a correlation window with relevant data"""
        from app.services.data_storage import data_storage

        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=window_minutes)

        # Get market data
        market_data = await data_storage.get_recent_market_data(symbol, hours=window_minutes/60)

        # Get social mentions
        social_mentions = await data_storage.get_recent_social_mentions(symbol, hours=window_minutes/60)

        # TODO: Get events when implemented
        events = []

        return CorrelationWindow(
            symbol=symbol,
            start_time=start_time,
            end_time=end_time,
            market_data=market_data,
            events=events,
            social_mentions=social_mentions
        )
    
    def get_registered_detectors(self) -> List[str]:
        """Get list of registered detector names"""
        return [detector.name for detector in self.detectors]


# Global pattern detection engine instance
pattern_engine = PatternDetectionEngine()
