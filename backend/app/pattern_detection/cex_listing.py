"""
CEX Listing Pattern Detector

Detects patterns that suggest a cryptocurrency exchange listing announcement
is causing price movements.

Pattern Characteristics:
1. Significant price increase (>15% in 1 hour)
2. Volume spike (>3x normal volume)
3. Social media mentions of exchange names
4. Timing correlation between announcement and price movement
"""

from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import re

from app.pattern_detection.base import PatternDetector, PatternMatch, CorrelationWindow


class CEXListingDetector(PatternDetector):
    """Detects CEX listing announcement patterns"""
    
    def __init__(self, min_confidence: float = 0.7):
        super().__init__("cex_listing", min_confidence)
        
        # Major exchange keywords to look for in social mentions
        self.exchange_keywords = [
            'binance', 'coinbase', 'kraken', 'okx', 'bybit', 'kucoin',
            'huobi', 'gate.io', 'bitfinex', 'gemini', 'crypto.com',
            'listing', 'listed', 'available', 'trading', 'launch'
        ]
        
        # Minimum thresholds for pattern detection
        self.min_price_increase = 15.0  # 15% price increase
        self.min_volume_ratio = 3.0     # 3x volume increase
        self.max_time_window = 120      # 2 hours max correlation window
    
    async def detect(self, window: CorrelationWindow) -> Optional[PatternMatch]:
        """Detect CEX listing patterns"""
        try:
            # Check if we have sufficient data
            if len(window.market_data) < 2:
                self.logger.debug(f"Insufficient market data for {window.symbol}")
                return None
            
            # Calculate price movement
            price_change = self._calculate_price_change(window.market_data, minutes=60)
            if price_change is None or price_change < self.min_price_increase:
                self.logger.debug(f"Price change {price_change}% below threshold for {window.symbol}")
                return None
            
            # Calculate volume spike
            volume_ratio = self._calculate_volume_ratio(window.market_data, minutes=60)
            if volume_ratio is None or volume_ratio < self.min_volume_ratio:
                self.logger.debug(f"Volume ratio {volume_ratio} below threshold for {window.symbol}")
                return None
            
            # Look for exchange-related social mentions
            exchange_mentions = self._find_exchange_mentions(window.social_mentions)
            
            # Calculate confidence based on multiple factors
            confidence = self._calculate_listing_confidence(
                price_change, volume_ratio, exchange_mentions, window
            )
            
            if confidence < self.min_confidence:
                self.logger.debug(f"Confidence {confidence:.2f} below threshold for {window.symbol}")
                return None
            
            # Create pattern match
            evidence = {
                'price_change_percent': round(price_change, 2),
                'volume_ratio': round(volume_ratio, 2),
                'exchange_mentions': len(exchange_mentions),
                'mention_details': exchange_mentions[:3],  # First 3 mentions
                'detection_time': datetime.now().isoformat(),
                'analysis_window_minutes': window.duration_minutes
            }
            
            description = (f"Potential CEX listing for {window.symbol}: "
                         f"{price_change:.1f}% price increase, "
                         f"{volume_ratio:.1f}x volume spike")
            
            if exchange_mentions:
                description += f", {len(exchange_mentions)} exchange mentions"
            
            return PatternMatch(
                pattern_type="cex_listing",
                symbol=window.symbol,
                confidence=confidence,
                trigger_time=window.end_time,
                evidence=evidence,
                description=description
            )
            
        except Exception as e:
            self.logger.error(f"Error detecting CEX listing pattern for {window.symbol}: {e}")
            return None
    
    def _find_exchange_mentions(self, social_mentions: List[Dict]) -> List[Dict]:
        """Find social mentions related to exchange listings"""
        exchange_mentions = []
        
        for mention in social_mentions:
            content = mention.get('content', '').lower()
            
            # Check for exchange keywords
            found_keywords = []
            for keyword in self.exchange_keywords:
                if keyword in content:
                    found_keywords.append(keyword)
            
            if found_keywords:
                mention_copy = mention.copy()
                mention_copy['matched_keywords'] = found_keywords
                exchange_mentions.append(mention_copy)
        
        return exchange_mentions
    
    def _calculate_listing_confidence(self, price_change: float, volume_ratio: float, 
                                    exchange_mentions: List[Dict], window: CorrelationWindow) -> float:
        """Calculate confidence score for CEX listing pattern"""
        
        # Base factors with their contributions
        factors = {}
        
        # Price change factor (0-1 scale)
        # 15% = 0.5, 30% = 0.8, 50%+ = 1.0
        price_factor = min(1.0, (price_change - 15) / 35 + 0.5)
        factors['price_change'] = price_factor
        
        # Volume factor (0-1 scale)
        # 3x = 0.5, 5x = 0.8, 10x+ = 1.0
        volume_factor = min(1.0, (volume_ratio - 3) / 7 + 0.5)
        factors['volume_spike'] = volume_factor
        
        # Social mentions factor
        if exchange_mentions:
            # More mentions = higher confidence, but with diminishing returns
            mention_factor = min(1.0, len(exchange_mentions) / 5)
            factors['social_mentions'] = mention_factor
        else:
            factors['social_mentions'] = 0.0
        
        # Timing factor - recent events are more relevant
        timing_factor = self._calculate_timing_factor(window)
        factors['timing'] = timing_factor
        
        # Weights for different factors
        weights = {
            'price_change': 0.35,    # Price movement is most important
            'volume_spike': 0.30,    # Volume confirms the movement
            'social_mentions': 0.25, # Social confirmation
            'timing': 0.10          # Timing relevance
        }
        
        confidence = self._calculate_confidence(factors, weights)
        
        self.logger.debug(f"CEX listing confidence calculation for {window.symbol}: "
                         f"factors={factors}, confidence={confidence:.3f}")
        
        return confidence
    
    def _calculate_timing_factor(self, window: CorrelationWindow) -> float:
        """Calculate timing relevance factor"""
        # For now, assume recent data is always relevant
        # In a real implementation, this would consider:
        # - Time since last major price movement
        # - Market hours vs off-hours
        # - Day of week patterns
        return 0.8
    
    def get_pattern_description(self) -> str:
        """Get description of CEX listing pattern"""
        return (
            "CEX Listing Pattern: Detects cryptocurrency exchange listing announcements "
            "by analyzing price increases (>15%), volume spikes (>3x), and social media "
            "mentions of major exchanges. High confidence patterns typically show "
            "coordinated price/volume movements with social confirmation."
        )


class WhaleAccumulationDetector(PatternDetector):
    """Detects whale accumulation patterns"""
    
    def __init__(self, min_confidence: float = 0.7):
        super().__init__("whale_accumulation", min_confidence)
        
        # Thresholds for whale activity
        self.min_volume_increase = 2.0  # 2x volume increase
        self.min_price_stability = 0.95  # Price should be relatively stable during accumulation
        self.accumulation_window = 240   # 4 hours accumulation window
    
    async def detect(self, window: CorrelationWindow) -> Optional[PatternMatch]:
        """Detect whale accumulation patterns"""
        try:
            if len(window.market_data) < 3:
                return None
            
            # Look for sustained volume increase with price stability
            volume_ratio = self._calculate_volume_ratio(window.market_data, minutes=240)
            price_volatility = self._calculate_price_volatility(window.market_data)
            
            if volume_ratio is None or volume_ratio < self.min_volume_increase:
                return None
            
            if price_volatility is None or price_volatility > (1 - self.min_price_stability):
                return None
            
            # Calculate confidence
            confidence = self._calculate_accumulation_confidence(volume_ratio, price_volatility)
            
            if confidence < self.min_confidence:
                return None
            
            evidence = {
                'volume_ratio': round(volume_ratio, 2),
                'price_volatility': round(price_volatility, 3),
                'accumulation_window_minutes': self.accumulation_window,
                'detection_time': datetime.now().isoformat()
            }
            
            description = (f"Potential whale accumulation for {window.symbol}: "
                         f"{volume_ratio:.1f}x volume increase with low volatility")
            
            return PatternMatch(
                pattern_type="whale_accumulation",
                symbol=window.symbol,
                confidence=confidence,
                trigger_time=window.end_time,
                evidence=evidence,
                description=description
            )
            
        except Exception as e:
            self.logger.error(f"Error detecting whale accumulation for {window.symbol}: {e}")
            return None
    
    def _calculate_price_volatility(self, market_data: List[Dict]) -> Optional[float]:
        """Calculate price volatility (coefficient of variation)"""
        if len(market_data) < 2:
            return None
        
        prices = [d['price'] for d in market_data if d['price'] is not None]
        if len(prices) < 2:
            return None
        
        mean_price = sum(prices) / len(prices)
        if mean_price == 0:
            return None
        
        variance = sum((p - mean_price) ** 2 for p in prices) / len(prices)
        std_dev = variance ** 0.5
        
        return std_dev / mean_price  # Coefficient of variation
    
    def _calculate_accumulation_confidence(self, volume_ratio: float, price_volatility: float) -> float:
        """Calculate confidence for whale accumulation"""
        factors = {
            'volume_increase': min(1.0, (volume_ratio - 2) / 3),  # 2x = 0, 5x = 1
            'price_stability': max(0.0, 1 - price_volatility * 10)  # Lower volatility = higher score
        }
        
        weights = {
            'volume_increase': 0.6,
            'price_stability': 0.4
        }
        
        return self._calculate_confidence(factors, weights)
    
    def get_pattern_description(self) -> str:
        """Get description of whale accumulation pattern"""
        return (
            "Whale Accumulation Pattern: Detects large-scale accumulation by identifying "
            "sustained volume increases (>2x) combined with price stability, suggesting "
            "institutional or whale buying without causing significant price impact."
        )
