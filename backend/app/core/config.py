"""
Application configuration using Pydantic settings
"""

import os
from typing import Optional
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """Application settings"""
    
    # Database
    DATABASE_URL: str = "postgresql://nexus_user:nexus_password@localhost:5432/nexus_db"
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # API Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    DEBUG: bool = True
    LOG_LEVEL: str = "INFO"
    SECRET_KEY: str = "dev-secret-key-change-in-production"
    
    # Frontend
    FRONTEND_URL: str = "http://localhost:3000"
    
    # Celery
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # API Keys
    COINGECKO_API_KEY: Optional[str] = None
    REDDIT_CLIENT_ID: Optional[str] = None
    REDDIT_CLIENT_SECRET: Optional[str] = None
    REDDIT_USER_AGENT: str = "nexus-bot/1.0"
    TWITTER_BEARER_TOKEN: Optional[str] = None
    GITHUB_TOKEN: Optional[str] = None
    
    # Pattern Detection
    MIN_CONFIDENCE_THRESHOLD: float = 0.70
    CORRELATION_WINDOW_MINUTES: int = 60
    MAX_SYMBOLS_TO_MONITOR: int = 50
    
    # Rate Limiting
    COINGECKO_RATE_LIMIT: int = 45
    REDDIT_RATE_LIMIT: int = 90
    BINANCE_RECONNECT_DELAY: int = 5
    
    # Monitoring
    PROMETHEUS_PORT: int = 9090
    GRAFANA_PORT: int = 3001
    
    @validator("DATABASE_URL", pre=True)
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
