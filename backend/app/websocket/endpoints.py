"""
WebSocket API endpoints for real-time communication
"""

import asyncio
import json
import logging
from typing import Optional
from fastapi import <PERSON>Router, WebSocket, WebSocketDisconnect, Query
from fastapi.responses import HTMLResponse

from app.websocket.manager import connection_manager

logger = logging.getLogger(__name__)

# Create WebSocket router
ws_router = APIRouter()


@ws_router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, client_id: Optional[str] = Query(None)):
    """Main WebSocket endpoint for real-time data streaming"""
    
    client_id = await connection_manager.connect(websocket, client_id)
    
    try:
        while True:
            # Wait for messages from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_client_message(client_id, message)
            except json.JSONDecodeError:
                await connection_manager.send_personal_message(client_id, {
                    'type': 'error',
                    'message': 'Invalid JSON format',
                    'timestamp': None
                })
            except Exception as e:
                logger.error(f"Error handling message from client {client_id}: {e}")
                await connection_manager.send_personal_message(client_id, {
                    'type': 'error',
                    'message': 'Internal server error',
                    'timestamp': None
                })
    
    except WebSocketDisconnect:
        await connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        await connection_manager.disconnect(client_id)


async def handle_client_message(client_id: str, message: dict):
    """Handle incoming messages from WebSocket clients"""
    
    message_type = message.get('type')
    
    if message_type == 'ping':
        # Respond to ping
        await connection_manager.send_personal_message(client_id, {
            'type': 'pong',
            'timestamp': None
        })
    
    elif message_type == 'get_status':
        # Send current system status
        from app.services.data_collector import data_collector
        from app.services.pattern_service import pattern_service
        
        status = {
            'data_collection': {
                'is_running': data_collector.is_running,
                'symbols_monitored': len(data_collector.monitored_symbols)
            },
            'pattern_detection': {
                'is_running': pattern_service.is_running,
                'detectors_count': len(pattern_service._initialize_detectors() or [])
            },
            'websocket': connection_manager.get_connection_stats()
        }
        
        await connection_manager.send_personal_message(client_id, {
            'type': 'system_status',
            'status': status,
            'timestamp': None
        })
    
    elif message_type == 'get_recent_patterns':
        # Send recent patterns
        from app.services.pattern_service import pattern_service
        
        hours = message.get('hours', 24)
        patterns = await pattern_service.get_recent_patterns(hours=hours)
        
        await connection_manager.send_personal_message(client_id, {
            'type': 'recent_patterns',
            'patterns': patterns,
            'hours': hours,
            'timestamp': None
        })
    
    else:
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'message': f'Unknown message type: {message_type}',
            'timestamp': None
        })


@ws_router.get("/ws/test")
async def websocket_test_page():
    """Test page for WebSocket connections"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Nexus WebSocket Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 800px; margin: 0 auto; }
            .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
            .connected { background-color: #d4edda; color: #155724; }
            .disconnected { background-color: #f8d7da; color: #721c24; }
            .message { background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-left: 3px solid #007bff; }
            .controls { margin: 20px 0; }
            button { padding: 10px 15px; margin: 5px; cursor: pointer; }
            #messages { height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Nexus WebSocket Test</h1>
            
            <div id="status" class="status disconnected">
                Status: Disconnected
            </div>
            
            <div class="controls">
                <button onclick="connect()">Connect</button>
                <button onclick="disconnect()">Disconnect</button>
                <button onclick="sendPing()">Send Ping</button>
                <button onclick="getStatus()">Get Status</button>
                <button onclick="getPatterns()">Get Recent Patterns</button>
                <button onclick="clearMessages()">Clear Messages</button>
            </div>
            
            <div id="messages"></div>
        </div>

        <script>
            let ws = null;
            let clientId = null;

            function connect() {
                if (ws) {
                    ws.close();
                }
                
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;
                
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    updateStatus('Connected', true);
                    addMessage('Connected to WebSocket', 'info');
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    addMessage(`Received: ${JSON.stringify(data, null, 2)}`, 'received');
                    
                    if (data.type === 'connection_established') {
                        clientId = data.client_id;
                        addMessage(`Client ID: ${clientId}`, 'info');
                    }
                };
                
                ws.onclose = function(event) {
                    updateStatus('Disconnected', false);
                    addMessage('WebSocket connection closed', 'info');
                };
                
                ws.onerror = function(error) {
                    addMessage(`WebSocket error: ${error}`, 'error');
                };
            }

            function disconnect() {
                if (ws) {
                    ws.close();
                    ws = null;
                }
            }

            function sendMessage(message) {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify(message));
                    addMessage(`Sent: ${JSON.stringify(message)}`, 'sent');
                } else {
                    addMessage('WebSocket not connected', 'error');
                }
            }

            function sendPing() {
                sendMessage({ type: 'ping' });
            }

            function getStatus() {
                sendMessage({ type: 'get_status' });
            }

            function getPatterns() {
                sendMessage({ type: 'get_recent_patterns', hours: 24 });
            }

            function updateStatus(text, connected) {
                const statusEl = document.getElementById('status');
                statusEl.textContent = `Status: ${text}`;
                statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
            }

            function addMessage(text, type) {
                const messagesEl = document.getElementById('messages');
                const messageEl = document.createElement('div');
                messageEl.className = 'message';
                messageEl.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${text}`;
                messagesEl.appendChild(messageEl);
                messagesEl.scrollTop = messagesEl.scrollHeight;
            }

            function clearMessages() {
                document.getElementById('messages').innerHTML = '';
            }

            // Auto-connect on page load
            window.onload = function() {
                connect();
            };
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@ws_router.get("/ws/stats")
async def websocket_stats():
    """Get WebSocket connection statistics"""
    return connection_manager.get_connection_stats()
