"""
WebSocket Connection Manager

Handles WebSocket connections, broadcasting, and real-time data streaming
without complex subscription management.
"""

import asyncio
import json
import logging
from typing import Dict, List, Set, Any, Optional
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
import uuid

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections and broadcasting"""
    
    def __init__(self):
        # Active connections
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Connection metadata
        self.connection_info: Dict[str, Dict[str, Any]] = {}
        
        # Broadcast queues for different data types
        self.market_data_queue: asyncio.Queue = asyncio.Queue()
        self.pattern_alert_queue: asyncio.Queue = asyncio.Queue()
        
        # Background tasks
        self.broadcast_tasks: List[asyncio.Task] = []
        self.is_running = False
    
    async def connect(self, websocket: WebSocket, client_id: Optional[str] = None) -> str:
        """Accept a new WebSocket connection"""
        await websocket.accept()
        
        # Generate client ID if not provided
        if not client_id:
            client_id = str(uuid.uuid4())
        
        # Store connection
        self.active_connections[client_id] = websocket
        self.connection_info[client_id] = {
            'connected_at': datetime.now(),
            'last_ping': datetime.now(),
            'client_info': {}
        }
        
        logger.info(f"WebSocket client {client_id} connected. Total connections: {len(self.active_connections)}")
        
        # Send welcome message
        await self.send_personal_message(client_id, {
            'type': 'connection_established',
            'client_id': client_id,
            'timestamp': datetime.now().isoformat(),
            'message': 'Connected to Nexus real-time data stream'
        })
        
        return client_id
    
    async def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            del self.connection_info[client_id]
            logger.info(f"WebSocket client {client_id} disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, client_id: str, message: Dict[str, Any]):
        """Send message to a specific client"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Error sending message to client {client_id}: {e}")
                await self.disconnect(client_id)
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients"""
        if not self.active_connections:
            return
        
        message_json = json.dumps(message, default=str)
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message_json)
            except Exception as e:
                logger.error(f"Error broadcasting to client {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            await self.disconnect(client_id)
    
    async def broadcast_market_data(self, symbol: str, data: Dict[str, Any]):
        """Broadcast market data update"""
        message = {
            'type': 'market_data',
            'symbol': symbol,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        await self.broadcast_message(message)
    
    async def broadcast_pattern_alert(self, pattern: Dict[str, Any]):
        """Broadcast pattern detection alert"""
        message = {
            'type': 'pattern_alert',
            'pattern': pattern,
            'timestamp': datetime.now().isoformat()
        }
        await self.broadcast_message(message)
    
    async def broadcast_system_status(self, status: Dict[str, Any]):
        """Broadcast system status update"""
        message = {
            'type': 'system_status',
            'status': status,
            'timestamp': datetime.now().isoformat()
        }
        await self.broadcast_message(message)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            'total_connections': len(self.active_connections),
            'active_clients': list(self.active_connections.keys()),
            'connection_info': {
                client_id: {
                    'connected_at': info['connected_at'].isoformat(),
                    'last_ping': info['last_ping'].isoformat()
                }
                for client_id, info in self.connection_info.items()
            }
        }
    
    async def ping_clients(self):
        """Send ping to all clients to keep connections alive"""
        ping_message = {
            'type': 'ping',
            'timestamp': datetime.now().isoformat()
        }
        await self.broadcast_message(ping_message)
        
        # Update last ping time
        current_time = datetime.now()
        for client_id in self.active_connections:
            if client_id in self.connection_info:
                self.connection_info[client_id]['last_ping'] = current_time
    
    async def start_background_tasks(self):
        """Start background tasks for broadcasting and maintenance"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Start ping task (every 30 seconds)
        ping_task = asyncio.create_task(self._ping_loop())
        self.broadcast_tasks.append(ping_task)
        
        logger.info("WebSocket background tasks started")
    
    async def stop_background_tasks(self):
        """Stop all background tasks"""
        self.is_running = False
        
        for task in self.broadcast_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.broadcast_tasks:
            await asyncio.gather(*self.broadcast_tasks, return_exceptions=True)
        
        self.broadcast_tasks.clear()
        logger.info("WebSocket background tasks stopped")
    
    async def _ping_loop(self):
        """Background task to ping clients periodically"""
        while self.is_running:
            try:
                await asyncio.sleep(30)  # Ping every 30 seconds
                if self.active_connections:
                    await self.ping_clients()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in ping loop: {e}")


# Global connection manager instance
connection_manager = ConnectionManager()
