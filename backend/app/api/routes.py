"""
API routes for the Nexus application
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import logging

from app.core.database import get_db
from app.services.data_collector import data_collector
from app.services.data_storage import data_storage

logger = logging.getLogger(__name__)

# Create main API router
api_router = APIRouter()


@api_router.get("/status")
async def get_status():
    """Get application status"""
    return {
        "status": "running",
        "service": "nexus-api",
        "version": "1.0.0"
    }


@api_router.get("/feed")
async def get_live_feed(
    limit: int = 20,
    symbols: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get live correlation feed"""
    # TODO: Implement actual feed logic
    return {
        "correlations": [],
        "total": 0,
        "limit": limit,
        "symbols": symbols.split(",") if symbols else None
    }


@api_router.get("/alerts")
async def get_recent_alerts(
    limit: int = 10,
    db: AsyncSession = Depends(get_db)
):
    """Get recent alerts"""
    # TODO: Implement actual alerts logic
    return {
        "alerts": [],
        "total": 0,
        "limit": limit
    }


@api_router.get("/symbols")
async def get_monitored_symbols():
    """Get list of monitored symbols"""
    symbols = await data_storage.get_symbols_with_data()
    return {
        "symbols": symbols,
        "total": len(symbols)
    }


@api_router.get("/patterns")
async def get_pattern_types():
    """Get available pattern types"""
    return {
        "patterns": [
            {
                "type": "cex_listing",
                "name": "CEX Listing",
                "description": "Exchange listing announcements"
            },
            {
                "type": "whale_accumulation",
                "name": "Whale Accumulation",
                "description": "Large wallet accumulation patterns"
            },
            {
                "type": "social_sentiment",
                "name": "Social Sentiment",
                "description": "Social media sentiment spikes"
            }
        ]
    }


@api_router.get("/data/collection-status")
async def get_collection_status():
    """Get data collection status"""
    status = await data_collector.get_collection_status()
    return status


@api_router.post("/data/start-collection")
async def start_data_collection():
    """Start data collection"""
    if data_collector.is_running:
        return {"message": "Data collection is already running", "status": "running"}

    # Start collection in background
    import asyncio
    asyncio.create_task(data_collector.start_collection())

    return {"message": "Data collection started", "status": "starting"}


@api_router.post("/data/stop-collection")
async def stop_data_collection():
    """Stop data collection"""
    await data_collector.stop_collection()
    return {"message": "Data collection stopped", "status": "stopped"}


@api_router.get("/data/market/{symbol}")
async def get_market_data(symbol: str, hours: int = 24):
    """Get recent market data for a symbol"""
    data = await data_storage.get_recent_market_data(symbol.upper(), hours)
    return {
        "symbol": symbol.upper(),
        "hours": hours,
        "data_points": len(data),
        "data": data
    }


@api_router.post("/data/symbols/{symbol}")
async def add_symbol_to_monitoring(symbol: str):
    """Add a symbol to monitoring"""
    await data_collector.add_symbol(symbol.upper())
    return {"message": f"Added {symbol.upper()} to monitoring"}


@api_router.delete("/data/symbols/{symbol}")
async def remove_symbol_from_monitoring(symbol: str):
    """Remove a symbol from monitoring"""
    await data_collector.remove_symbol(symbol.upper())
    return {"message": f"Removed {symbol.upper()} from monitoring"}
