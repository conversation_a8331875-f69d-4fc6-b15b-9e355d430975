#!/usr/bin/env python3
"""
Test script for pattern detection system
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.pattern_detection.base import CorrelationWindow
from app.pattern_detection.cex_listing import CEXListingDetector, WhaleAccumulationDetector


async def test_cex_listing_detection():
    """Test CEX listing pattern detection with simulated data"""
    print("🧪 Testing CEX Listing Pattern Detection")
    
    detector = CEXListingDetector(min_confidence=0.6)  # Lower threshold for testing
    
    # Create simulated data for a CEX listing scenario
    now = datetime.now()
    
    # Simulate price data showing a 24% increase with clear 5x volume spike
    market_data = [
        {
            'timestamp': now - timedelta(minutes=120),
            'symbol': 'TEST',
            'price': 100.0,
            'volume': 1000000,
            'source': 'test'
        },
        {
            'timestamp': now - timedelta(minutes=90),
            'symbol': 'TEST',
            'price': 102.0,
            'volume': 1100000,
            'source': 'test'
        },
        {
            'timestamp': now - timedelta(minutes=60),
            'symbol': 'TEST',
            'price': 105.0,
            'volume': 1200000,
            'source': 'test'
        },
        {
            'timestamp': now - timedelta(minutes=30),
            'symbol': 'TEST',
            'price': 125.0,
            'volume': 1300000,
            'source': 'test'
        },
        {
            'timestamp': now,
            'symbol': 'TEST',
            'price': 130.0,  # 24% increase from 60min ago
            'volume': 6000000,  # 5.2x volume spike
            'source': 'test'
        }
    ]
    
    # Simulate social mentions about exchange listing (multiple mentions for higher confidence)
    social_mentions = [
        {
            'timestamp': now - timedelta(minutes=45),
            'platform': 'reddit',
            'content': 'TEST token is getting listed on Binance tomorrow!',
            'engagement_score': 85.0,
            'sentiment_score': 0.8
        },
        {
            'timestamp': now - timedelta(minutes=30),
            'platform': 'reddit',
            'content': 'Binance just announced TEST listing, huge news!',
            'engagement_score': 120.0,
            'sentiment_score': 0.9
        },
        {
            'timestamp': now - timedelta(minutes=25),
            'platform': 'reddit',
            'content': 'Coinbase also listing TEST next week according to their blog',
            'engagement_score': 95.0,
            'sentiment_score': 0.7
        },
        {
            'timestamp': now - timedelta(minutes=15),
            'platform': 'reddit',
            'content': 'Multiple exchanges listing TEST, this is going to moon!',
            'engagement_score': 150.0,
            'sentiment_score': 0.9
        }
    ]
    
    # Create correlation window
    window = CorrelationWindow(
        symbol='TEST',
        start_time=now - timedelta(minutes=120),
        end_time=now,
        market_data=market_data,
        events=[],
        social_mentions=social_mentions
    )
    
    # Run detection
    pattern = await detector.detect(window)
    
    if pattern:
        print(f"✅ CEX Listing Pattern Detected!")
        print(f"   Symbol: {pattern.symbol}")
        print(f"   Confidence: {pattern.confidence:.2f}")
        print(f"   Description: {pattern.description}")
        print(f"   Evidence: {pattern.evidence}")
        return True
    else:
        print("❌ No CEX listing pattern detected")
        return False


async def test_whale_accumulation_detection():
    """Test whale accumulation pattern detection"""
    print("\n🧪 Testing Whale Accumulation Pattern Detection")
    
    detector = WhaleAccumulationDetector(min_confidence=0.4)  # Even lower threshold for testing
    
    now = datetime.now()
    
    # Simulate stable price with high volume (accumulation)
    market_data = [
        {
            'timestamp': now - timedelta(minutes=300),
            'symbol': 'TEST2',
            'price': 50.0,
            'volume': 800000,
            'source': 'test'
        },
        {
            'timestamp': now - timedelta(minutes=240),
            'symbol': 'TEST2',
            'price': 50.1,
            'volume': 900000,
            'source': 'test'
        },
        {
            'timestamp': now - timedelta(minutes=180),
            'symbol': 'TEST2',
            'price': 49.9,
            'volume': 1000000,
            'source': 'test'
        },
        {
            'timestamp': now - timedelta(minutes=120),
            'symbol': 'TEST2',
            'price': 50.0,
            'volume': 1100000,
            'source': 'test'
        },
        {
            'timestamp': now - timedelta(minutes=60),
            'symbol': 'TEST2',
            'price': 50.2,
            'volume': 1200000,
            'source': 'test'
        },
        {
            'timestamp': now,
            'symbol': 'TEST2',
            'price': 50.1,  # Very stable price (0.2% volatility)
            'volume': 2400000,  # 2.4x volume increase from average
            'source': 'test'
        }
    ]
    
    # Create correlation window
    window = CorrelationWindow(
        symbol='TEST2',
        start_time=now - timedelta(minutes=240),
        end_time=now,
        market_data=market_data,
        events=[],
        social_mentions=[]
    )
    
    # Run detection
    pattern = await detector.detect(window)
    
    if pattern:
        print(f"✅ Whale Accumulation Pattern Detected!")
        print(f"   Symbol: {pattern.symbol}")
        print(f"   Confidence: {pattern.confidence:.2f}")
        print(f"   Description: {pattern.description}")
        print(f"   Evidence: {pattern.evidence}")
        return True
    else:
        print("❌ No whale accumulation pattern detected")
        return False


async def test_no_pattern_detection():
    """Test that no patterns are detected for normal market data"""
    print("\n🧪 Testing Normal Market Data (Should Detect No Patterns)")
    
    cex_detector = CEXListingDetector(min_confidence=0.7)
    
    now = datetime.now()
    
    # Normal market data - small movements, normal volume
    market_data = [
        {
            'timestamp': now - timedelta(minutes=60),
            'symbol': 'NORMAL',
            'price': 100.0,
            'volume': 1000000,
            'source': 'test'
        },
        {
            'timestamp': now - timedelta(minutes=30),
            'symbol': 'NORMAL',
            'price': 102.0,  # Only 2% increase
            'volume': 1100000,  # Only 10% volume increase
            'source': 'test'
        },
        {
            'timestamp': now,
            'symbol': 'NORMAL',
            'price': 101.5,
            'volume': 950000,
            'source': 'test'
        }
    ]
    
    window = CorrelationWindow(
        symbol='NORMAL',
        start_time=now - timedelta(minutes=60),
        end_time=now,
        market_data=market_data,
        events=[],
        social_mentions=[]
    )
    
    pattern = await cex_detector.detect(window)
    
    if pattern:
        print(f"❌ Unexpected pattern detected: {pattern.pattern_type}")
        return False
    else:
        print("✅ Correctly detected no patterns in normal market data")
        return True


async def main():
    """Run all pattern detection tests"""
    print("🚀 Starting Pattern Detection Tests\n")
    
    tests = [
        test_cex_listing_detection(),
        test_whale_accumulation_detection(),
        test_no_pattern_detection()
    ]
    
    results = await asyncio.gather(*tests)
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All pattern detection tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
