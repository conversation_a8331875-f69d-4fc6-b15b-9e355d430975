@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for Nexus Application */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #0f172a;
  color: #e2e8f0;
}

/* Custom component styles */
@layer components {
  .nexus-card {
    @apply bg-slate-800 border border-slate-700 rounded-lg shadow-lg;
  }

  .nexus-button {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .nexus-button-primary {
    @apply nexus-button bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500;
  }

  .nexus-button-secondary {
    @apply nexus-button bg-slate-700 text-slate-200 hover:bg-slate-600 focus:ring-2 focus:ring-slate-500;
  }

  .nexus-input {
    @apply px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-connected {
    @apply status-indicator bg-green-100 text-green-800;
  }

  .status-disconnected {
    @apply status-indicator bg-red-100 text-red-800;
  }

  .status-connecting {
    @apply status-indicator bg-yellow-100 text-yellow-800;
  }
}
