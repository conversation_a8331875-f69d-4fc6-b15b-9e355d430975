/**
 * Connection Indicator Component
 * 
 * Displays real-time WebSocket connection status with visual indicators
 * and connection management controls.
 */

import React from 'react';
import type { ConnectionState } from '../../types/api';

interface ConnectionIndicatorProps {
  connectionState: ConnectionState;
  onConnect?: () => void;
  onDisconnect?: () => void;
  showControls?: boolean;
  compact?: boolean;
}

export const ConnectionIndicator: React.FC<ConnectionIndicatorProps> = ({
  connectionState,
  onConnect,
  onDisconnect,
  showControls = false,
  compact = false,
}) => {
  const getStatusInfo = () => {
    if (connectionState.isConnected) {
      return {
        status: 'Connected',
        className: 'status-connected',
        icon: '🟢',
        description: 'Real-time data streaming active',
      };
    } else if (connectionState.isConnecting) {
      return {
        status: 'Connecting',
        className: 'status-connecting',
        icon: '🟡',
        description: 'Establishing connection...',
      };
    } else {
      return {
        status: 'Disconnected',
        className: 'status-disconnected',
        icon: '🔴',
        description: connectionState.error || 'Not connected to real-time data',
      };
    }
  };

  const statusInfo = getStatusInfo();

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <span className="text-sm">{statusInfo.icon}</span>
        <span className={`text-xs ${statusInfo.className}`}>
          {statusInfo.status}
        </span>
      </div>
    );
  }

  return (
    <div className="nexus-card p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{statusInfo.icon}</span>
            <div>
              <div className={`font-medium ${statusInfo.className}`}>
                {statusInfo.status}
              </div>
              <div className="text-sm text-slate-400">
                {statusInfo.description}
              </div>
            </div>
          </div>
        </div>

        {showControls && (
          <div className="flex space-x-2">
            {connectionState.isConnected ? (
              <button
                onClick={onDisconnect}
                className="nexus-button-secondary text-sm"
                disabled={!onDisconnect}
              >
                Disconnect
              </button>
            ) : (
              <button
                onClick={onConnect}
                className="nexus-button-primary text-sm"
                disabled={connectionState.isConnecting || !onConnect}
              >
                {connectionState.isConnecting ? 'Connecting...' : 'Connect'}
              </button>
            )}
          </div>
        )}
      </div>

      {/* Connection details */}
      {!compact && (
        <div className="mt-4 pt-4 border-t border-slate-700">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-slate-400">Reconnect Attempts:</span>
              <span className="ml-2 text-slate-200">
                {connectionState.reconnectAttempts}
              </span>
            </div>
            {connectionState.lastConnected && (
              <div>
                <span className="text-slate-400">Last Connected:</span>
                <span className="ml-2 text-slate-200">
                  {new Date(connectionState.lastConnected).toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>
          
          {connectionState.error && (
            <div className="mt-3 p-3 bg-danger-900/20 border border-danger-500/30 rounded-md">
              <div className="text-sm text-danger-400">
                <strong>Connection Error:</strong>
              </div>
              <div className="text-sm text-danger-300 mt-1">
                {connectionState.error}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Simplified status badge component
export const ConnectionBadge: React.FC<{
  connectionState: ConnectionState;
  className?: string;
}> = ({ connectionState, className = '' }) => {
  const statusInfo = (() => {
    if (connectionState.isConnected) {
      return { text: 'Live', className: 'bg-success-500' };
    } else if (connectionState.isConnecting) {
      return { text: 'Connecting', className: 'bg-warning-500 animate-pulse' };
    } else {
      return { text: 'Offline', className: 'bg-danger-500' };
    }
  })();

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${statusInfo.className}`} />
      <span className="text-xs font-medium text-slate-300">
        {statusInfo.text}
      </span>
    </div>
  );
};
