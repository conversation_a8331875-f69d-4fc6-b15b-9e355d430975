/**
 * Error Boundary Component - Production-Ready Error Handling
 * 
 * Catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI.
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log error details
    console.error('[ErrorBoundary] Caught an error:', error);
    console.error('[ErrorBoundary] Error info:', errorInfo);
    
    // Update state with error info
    this.setState({
      error,
      errorInfo,
    });

    // In production, you might want to send this to an error reporting service
    if (import.meta.env.PROD) {
      // Example: Send to error reporting service
      // errorReportingService.captureException(error, { extra: errorInfo });
    }
  }

  private handleRetry = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  private handleReload = (): void => {
    window.location.reload();
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
          <div className="nexus-card p-8 max-w-2xl w-full">
            <div className="text-center">
              <div className="text-danger-500 text-6xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold text-slate-200 mb-4">
                Something went wrong
              </h1>
              <p className="text-slate-400 mb-6">
                An unexpected error occurred in the Nexus application. 
                This error has been logged for investigation.
              </p>
              
              {/* Error details (only in development) */}
              {import.meta.env.DEV && this.state.error && (
                <div className="bg-slate-900 border border-slate-600 rounded-lg p-4 mb-6 text-left">
                  <h3 className="text-lg font-semibold text-danger-400 mb-2">
                    Error Details (Development Mode)
                  </h3>
                  <div className="text-sm text-slate-300 font-mono">
                    <div className="mb-2">
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    <div className="mb-2">
                      <strong>Stack:</strong>
                      <pre className="mt-1 text-xs overflow-x-auto whitespace-pre-wrap">
                        {this.state.error.stack}
                      </pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto whitespace-pre-wrap">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* Action buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={this.handleRetry}
                  className="nexus-button-primary"
                >
                  Try Again
                </button>
                <button
                  onClick={this.handleReload}
                  className="nexus-button-secondary"
                >
                  Reload Page
                </button>
                <a
                  href="/"
                  className="nexus-button-secondary inline-block text-center"
                >
                  Go Home
                </a>
              </div>
              
              {/* Support information */}
              <div className="mt-8 pt-6 border-t border-slate-700">
                <p className="text-sm text-slate-500">
                  If this problem persists, please check the browser console for more details
                  or contact support with the error information above.
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
