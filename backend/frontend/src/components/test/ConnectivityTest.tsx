/**
 * Connectivity Test Component
 * 
 * Tests API and WebSocket connectivity against validated backend endpoints
 */

import React, { useState, useEffect } from 'react';
import { api } from '../../services/api';
import { webSocketService } from '../../services/websocket';
import { ConnectionIndicator } from '../common/ConnectionIndicator';
import type { ConnectionState, WebSocketMessage } from '../../types/api';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  timestamp?: string;
}

export const ConnectivityTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    reconnectAttempts: 0,
    lastConnected: null,
  });
  const [wsMessages, setWsMessages] = useState<WebSocketMessage[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    // Subscribe to WebSocket connection changes
    const unsubscribeConnection = webSocketService.onConnectionChange(setConnectionState);
    
    // Subscribe to WebSocket messages
    const unsubscribeMessages = webSocketService.onMessage((message) => {
      setWsMessages(prev => [message, ...prev.slice(0, 9)]); // Keep last 10 messages
    });

    return () => {
      unsubscribeConnection();
      unsubscribeMessages();
    };
  }, []);

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, { ...result, timestamp: new Date().toISOString() }]);
  };

  const runConnectivityTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    // Test 1: System Health
    addTestResult({ name: 'System Health', status: 'pending', message: 'Testing...' });
    try {
      const health = await api.health();
      addTestResult({
        name: 'System Health',
        status: 'success',
        message: `Status: ${health.status}`,
      });
    } catch (error) {
      addTestResult({
        name: 'System Health',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 2: System Status
    addTestResult({ name: 'System Status', status: 'pending', message: 'Testing...' });
    try {
      const status = await api.status();
      addTestResult({
        name: 'System Status',
        status: 'success',
        message: `Service: ${status.service || 'nexus-api'}, Version: ${status.version || '1.0.0'}`,
      });
    } catch (error) {
      addTestResult({
        name: 'System Status',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 3: Data Collection Status
    addTestResult({ name: 'Data Collection Status', status: 'pending', message: 'Testing...' });
    try {
      const dataStatus = await api.dataStatus();
      addTestResult({
        name: 'Data Collection Status',
        status: 'success',
        message: `Running: ${dataStatus.is_running}, Symbols: ${dataStatus.symbols_with_data_count}`,
      });
    } catch (error) {
      addTestResult({
        name: 'Data Collection Status',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 4: Pattern Detection Status
    addTestResult({ name: 'Pattern Detection Status', status: 'pending', message: 'Testing...' });
    try {
      const patternStatus = await api.patternStatus();
      addTestResult({
        name: 'Pattern Detection Status',
        status: 'success',
        message: `Running: ${patternStatus.is_running}, Detectors: ${patternStatus.registered_detectors.length}`,
      });
    } catch (error) {
      addTestResult({
        name: 'Pattern Detection Status',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 5: Real-time Status
    addTestResult({ name: 'Real-time Status', status: 'pending', message: 'Testing...' });
    try {
      const realtimeStatus = await api.realtimeStatus();
      addTestResult({
        name: 'Real-time Status',
        status: 'success',
        message: `Broadcasting: ${realtimeStatus.broadcasting.is_running}, Connections: ${realtimeStatus.websocket.total_connections}`,
      });
    } catch (error) {
      addTestResult({
        name: 'Real-time Status',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    setIsRunning(false);
  };

  const connectWebSocket = () => {
    webSocketService.connect();
  };

  const disconnectWebSocket = () => {
    webSocketService.disconnect();
  };

  const sendTestMessage = () => {
    if (connectionState.isConnected) {
      webSocketService.getStatus();
    }
  };

  return (
    <div className="space-y-6">
      <div className="nexus-card p-6">
        <h2 className="text-xl font-bold text-slate-200 mb-4">
          Backend Connectivity Test
        </h2>
        
        <div className="flex space-x-4 mb-6">
          <button
            onClick={runConnectivityTests}
            disabled={isRunning}
            className="nexus-button-primary"
          >
            {isRunning ? 'Running Tests...' : 'Run API Tests'}
          </button>
          
          <button
            onClick={() => setTestResults([])}
            className="nexus-button-secondary"
          >
            Clear Results
          </button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold text-slate-300">Test Results:</h3>
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded-md border ${
                  result.status === 'success'
                    ? 'bg-green-900/20 border-green-500/30 text-green-300'
                    : result.status === 'error'
                    ? 'bg-red-900/20 border-red-500/30 text-red-300'
                    : 'bg-yellow-900/20 border-yellow-500/30 text-yellow-300'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium">{result.name}</div>
                    <div className="text-sm opacity-90">{result.message}</div>
                  </div>
                  <div className="text-xs opacity-75">
                    {result.timestamp && new Date(result.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* WebSocket Test */}
      <div className="nexus-card p-6">
        <h3 className="text-lg font-semibold text-slate-200 mb-4">
          WebSocket Connection Test
        </h3>
        
        <ConnectionIndicator
          connectionState={connectionState}
          onConnect={connectWebSocket}
          onDisconnect={disconnectWebSocket}
          showControls={true}
        />

        <div className="mt-4 flex space-x-4">
          <button
            onClick={sendTestMessage}
            disabled={!connectionState.isConnected}
            className="nexus-button-secondary"
          >
            Send Test Message
          </button>
        </div>

        {/* WebSocket Messages */}
        {wsMessages.length > 0 && (
          <div className="mt-6">
            <h4 className="font-medium text-slate-300 mb-2">Recent Messages:</h4>
            <div className="space-y-2 max-h-64 overflow-y-auto scrollbar-thin">
              {wsMessages.map((message, index) => (
                <div
                  key={index}
                  className="p-2 bg-slate-900 border border-slate-600 rounded text-sm"
                >
                  <div className="flex justify-between items-start">
                    <span className="font-medium text-blue-400">
                      {message.type}
                    </span>
                    <span className="text-xs text-slate-500">
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <pre className="text-xs text-slate-300 mt-1 whitespace-pre-wrap">
                    {JSON.stringify(message, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
