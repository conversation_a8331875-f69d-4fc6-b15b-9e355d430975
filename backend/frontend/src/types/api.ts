/**
 * API Type Definitions - Validated Against Backend Implementation
 * 
 * These types are based on the confirmed backend API responses
 * from our comprehensive validation testing.
 */

// ============================================================================
// MARKET DATA TYPES (Validated against /api/v1/data/market/{symbol})
// ============================================================================

export interface MarketData {
  readonly timestamp: string;
  readonly symbol: string;
  readonly price: number;
  readonly volume: number | null;
  readonly market_cap: number | null;
  readonly source: string;
}

export interface MarketDataResponse {
  readonly symbol: string;
  readonly data_points: number;
  readonly hours: number;
  readonly data: readonly MarketData[];
}

// ============================================================================
// PATTERN DETECTION TYPES (Validated against /api/v1/patterns/*)
// ============================================================================

export type PatternType = 'cex_listing' | 'whale_accumulation';

export interface PatternEvidence {
  readonly price_change_percent?: number;
  readonly volume_ratio?: number;
  readonly exchange_mentions?: number;
  readonly mention_details?: readonly unknown[];
  readonly detection_time?: string;
  readonly analysis_window_minutes?: number;
  readonly price_volatility?: number;
  readonly accumulation_window_minutes?: number;
  readonly [key: string]: unknown;
}

export interface PatternAlert {
  readonly id?: string;
  readonly symbol: string;
  readonly pattern_type: PatternType;
  readonly confidence: number;
  readonly trigger_time: string;
  readonly evidence: PatternEvidence;
  readonly description: string;
  readonly status?: string;
  readonly created_at?: string;
}

export interface PatternAnalysisResponse {
  readonly symbol: string;
  readonly patterns: readonly PatternAlert[];
  readonly total: number;
  readonly analysis_time: string;
}

export interface RecentPatternsResponse {
  readonly patterns: readonly PatternAlert[];
  readonly total: number;
  readonly hours: number;
  readonly symbol?: string;
}

// ============================================================================
// SYSTEM STATUS TYPES (Validated against /api/v1/status and /api/v1/realtime/status)
// ============================================================================

export interface SystemStatus {
  readonly status: string;
  readonly timestamp: string;
  readonly version?: string;
  readonly environment?: string;
}

export interface DataCollectionStatus {
  readonly is_running: boolean;
  readonly monitored_symbols_count: number;
  readonly symbols_with_data_count: number;
  readonly binance_ws_connected?: boolean;
  readonly last_update?: string;
}

export interface PatternDetectionStatus {
  readonly is_running: boolean;
  readonly detection_interval_seconds: number;
  readonly analysis_window_minutes: number;
  readonly min_confidence_threshold: number;
  readonly registered_detectors: readonly string[];
  readonly symbols_monitored: number;
  readonly patterns_last_24h: number;
  readonly patterns_by_type: Record<string, number>;
  readonly last_cycle_time: string;
}

export interface WebSocketStats {
  readonly total_connections: number;
  readonly active_clients: readonly string[];
  readonly connection_info: Record<string, {
    readonly connected_at: string;
    readonly last_ping: string;
  }>;
}

export interface BroadcastingStatus {
  readonly is_running: boolean;
  readonly active_tasks: number;
  readonly market_data_interval: number;
  readonly pattern_check_interval: number;
  readonly status_update_interval: number;
  readonly active_connections: number;
  readonly last_status_broadcast: string | null;
}

export interface RealtimeStatus {
  readonly broadcasting: BroadcastingStatus;
  readonly websocket: WebSocketStats;
  readonly endpoints: {
    readonly websocket: string;
    readonly test_page: string;
    readonly stats: string;
  };
}

// ============================================================================
// SYMBOLS API TYPES (Validated against /api/v1/symbols)
// ============================================================================

export interface SymbolsResponse {
  readonly symbols: readonly string[];
  readonly total: number;
}

// ============================================================================
// WEBSOCKET MESSAGE TYPES (Validated against WebSocket implementation)
// ============================================================================

export type WebSocketMessageType = 
  | 'connection_established'
  | 'market_data'
  | 'pattern_alert'
  | 'system_status'
  | 'test_message'
  | 'ping'
  | 'pong'
  | 'recent_patterns'
  | 'error';

export interface BaseWebSocketMessage {
  readonly type: WebSocketMessageType;
  readonly timestamp: string;
}

export interface ConnectionEstablishedMessage extends BaseWebSocketMessage {
  readonly type: 'connection_established';
  readonly client_id: string;
  readonly message: string;
}

export interface MarketDataMessage extends BaseWebSocketMessage {
  readonly type: 'market_data';
  readonly symbol: string;
  readonly data: MarketData;
}

export interface PatternAlertMessage extends BaseWebSocketMessage {
  readonly type: 'pattern_alert';
  readonly pattern: PatternAlert;
}

export interface SystemStatusMessage extends BaseWebSocketMessage {
  readonly type: 'system_status';
  readonly status: {
    readonly data_collection: {
      readonly is_running: boolean;
      readonly symbols_monitored: number;
      readonly symbols_with_data: number;
      readonly binance_ws_connected: boolean;
    };
    readonly pattern_detection: {
      readonly is_running: boolean;
      readonly detectors_count: number;
      readonly patterns_last_hour: number;
      readonly confidence_threshold: number;
    };
    readonly websocket: {
      readonly active_connections: number;
      readonly broadcasting_active: boolean;
    };
    readonly timestamp: string;
  };
}

export interface TestMessage extends BaseWebSocketMessage {
  readonly type: 'test_message';
  readonly message: string;
}

export interface PingMessage extends BaseWebSocketMessage {
  readonly type: 'ping';
}

export interface PongMessage extends BaseWebSocketMessage {
  readonly type: 'pong';
}

export interface RecentPatternsMessage extends BaseWebSocketMessage {
  readonly type: 'recent_patterns';
  readonly patterns: readonly PatternAlert[];
  readonly hours: number;
}

export interface ErrorMessage extends BaseWebSocketMessage {
  readonly type: 'error';
  readonly message: string;
}

export type WebSocketMessage = 
  | ConnectionEstablishedMessage
  | MarketDataMessage
  | PatternAlertMessage
  | SystemStatusMessage
  | TestMessage
  | PingMessage
  | PongMessage
  | RecentPatternsMessage
  | ErrorMessage;

// ============================================================================
// CLIENT MESSAGE TYPES (Messages sent from frontend to backend)
// ============================================================================

export interface ClientPingMessage {
  readonly type: 'ping';
}

export interface ClientGetStatusMessage {
  readonly type: 'get_status';
}

export interface ClientGetRecentPatternsMessage {
  readonly type: 'get_recent_patterns';
  readonly hours?: number;
}

export type ClientMessage = 
  | ClientPingMessage
  | ClientGetStatusMessage
  | ClientGetRecentPatternsMessage;

// ============================================================================
// API ERROR TYPES
// ============================================================================

export interface ApiError {
  readonly message: string;
  readonly status?: number;
  readonly code?: string;
  readonly timestamp: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export interface LoadingState {
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly lastUpdated: string | null;
}

export interface ConnectionState {
  readonly isConnected: boolean;
  readonly isConnecting: boolean;
  readonly error: string | null;
  readonly reconnectAttempts: number;
  readonly lastConnected: string | null;
}
