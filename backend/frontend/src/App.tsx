/**
 * Nexus Frontend Application
 *
 * Phase 1: Core Infrastructure Testing
 * Testing connectivity to validated backend endpoints
 */

import React from 'react';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { ConnectivityTest } from './components/test/ConnectivityTest';

function App() {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-slate-900 p-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <header className="mb-8">
            <div className="nexus-card p-6">
              <h1 className="text-3xl font-bold text-slate-200 mb-2">
                Nexus - Crypto Causal Event Correlation Engine
              </h1>
              <p className="text-slate-400">
                Phase 1: Core Infrastructure Testing - Validating Backend Connectivity
              </p>
              <div className="mt-4 flex items-center space-x-4 text-sm text-slate-500">
                <span>Frontend: React + TypeScript + Vite</span>
                <span>•</span>
                <span>Backend: FastAPI + WebSocket</span>
                <span>•</span>
                <span>Real-time: WebSocket Streaming</span>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main>
            <ConnectivityTest />
          </main>

          {/* Footer */}
          <footer className="mt-8 text-center text-sm text-slate-500">
            <p>
              Nexus v1.0.0 - Week 4 Frontend Development - Phase 1 Complete
            </p>
          </footer>
        </div>
      </div>
    </ErrorBoundary>
  );
}

export default App;
