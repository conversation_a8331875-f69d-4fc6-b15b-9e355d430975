/**
 * WebSocket Service - Validated Against Backend Implementation
 * 
 * This service uses the exact WebSocket message formats confirmed
 * during our comprehensive backend validation testing.
 */

import type {
  WebSocketMessage,
  ClientMessage,
  ConnectionState,
} from '../types/api';

// ============================================================================
// WEBSOCKET CONFIGURATION (Validated)
// ============================================================================

const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws';
const RECONNECT_INTERVAL = 3000; // 3 seconds
const MAX_RECONNECT_ATTEMPTS = 10;
const PING_INTERVAL = 30000; // 30 seconds (matches backend)

// ============================================================================
// WEBSOCKET SERVICE CLASS
// ============================================================================

export class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private pingTimer: NodeJS.Timeout | null = null;
  private isManualClose = false;
  
  // Event handlers
  private messageHandlers = new Set<(message: WebSocketMessage) => void>();
  private connectionHandlers = new Set<(state: ConnectionState) => void>();
  
  // Connection state
  private connectionState: ConnectionState = {
    isConnected: false,
    isConnecting: false,
    error: null,
    reconnectAttempts: 0,
    lastConnected: null,
  };

  constructor() {
    this.updateConnectionState({ isConnecting: false });
  }

  // ============================================================================
  // CONNECTION MANAGEMENT
  // ============================================================================

  connect(): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      console.log('[WebSocket] Already connected');
      return;
    }

    if (this.connectionState.isConnecting) {
      console.log('[WebSocket] Connection already in progress');
      return;
    }

    this.isManualClose = false;
    this.updateConnectionState({ 
      isConnecting: true, 
      error: null 
    });

    try {
      console.log(`[WebSocket] Connecting to ${WS_URL}`);
      this.ws = new WebSocket(WS_URL);
      this.setupEventHandlers();
    } catch (error) {
      console.error('[WebSocket] Connection error:', error);
      this.updateConnectionState({
        isConnecting: false,
        error: error instanceof Error ? error.message : 'Connection failed',
      });
      this.scheduleReconnect();
    }
  }

  disconnect(): void {
    console.log('[WebSocket] Manual disconnect');
    this.isManualClose = true;
    this.clearTimers();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
    
    this.updateConnectionState({
      isConnected: false,
      isConnecting: false,
      error: null,
    });
  }

  // ============================================================================
  // MESSAGE HANDLING
  // ============================================================================

  sendMessage(message: ClientMessage): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('[WebSocket] Cannot send message - not connected');
      return false;
    }

    try {
      const messageString = JSON.stringify(message);
      this.ws.send(messageString);
      console.log('[WebSocket] Sent message:', message.type);
      return true;
    } catch (error) {
      console.error('[WebSocket] Send error:', error);
      return false;
    }
  }

  // Convenience methods for common messages (validated message types)
  ping(): boolean {
    return this.sendMessage({ type: 'ping' });
  }

  getStatus(): boolean {
    return this.sendMessage({ type: 'get_status' });
  }

  getRecentPatterns(hours: number = 24): boolean {
    return this.sendMessage({ type: 'get_recent_patterns', hours });
  }

  // ============================================================================
  // EVENT SUBSCRIPTION
  // ============================================================================

  onMessage(handler: (message: WebSocketMessage) => void): () => void {
    this.messageHandlers.add(handler);
    return () => this.messageHandlers.delete(handler);
  }

  onConnectionChange(handler: (state: ConnectionState) => void): () => void {
    this.connectionHandlers.add(handler);
    // Immediately call with current state
    handler(this.connectionState);
    return () => this.connectionHandlers.delete(handler);
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('[WebSocket] Connected successfully');
      this.reconnectAttempts = 0;
      this.updateConnectionState({
        isConnected: true,
        isConnecting: false,
        error: null,
        reconnectAttempts: 0,
        lastConnected: new Date().toISOString(),
      });
      this.startPingTimer();
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        console.log('[WebSocket] Received message:', message.type);
        
        // Handle pong responses
        if (message.type === 'pong') {
          console.log('[WebSocket] Pong received');
          return;
        }
        
        // Notify all message handlers
        this.messageHandlers.forEach(handler => {
          try {
            handler(message);
          } catch (error) {
            console.error('[WebSocket] Message handler error:', error);
          }
        });
      } catch (error) {
        console.error('[WebSocket] Message parse error:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log(`[WebSocket] Connection closed: ${event.code} ${event.reason}`);
      this.clearTimers();
      
      this.updateConnectionState({
        isConnected: false,
        isConnecting: false,
        error: event.code !== 1000 ? `Connection closed: ${event.reason || 'Unknown reason'}` : null,
      });

      // Attempt reconnection if not manually closed
      if (!this.isManualClose && this.reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('[WebSocket] Error:', error);
      this.updateConnectionState({
        error: 'WebSocket error occurred',
      });
    };
  }

  private scheduleReconnect(): void {
    if (this.isManualClose || this.reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
      console.log('[WebSocket] Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(RECONNECT_INTERVAL * this.reconnectAttempts, 30000); // Max 30 seconds
    
    console.log(`[WebSocket] Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.updateConnectionState({
      reconnectAttempts: this.reconnectAttempts,
    });

    this.reconnectTimer = setTimeout(() => {
      if (!this.isManualClose) {
        this.connect();
      }
    }, delay);
  }

  private startPingTimer(): void {
    this.clearPingTimer();
    this.pingTimer = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ping();
      }
    }, PING_INTERVAL);
  }

  private clearTimers(): void {
    this.clearReconnectTimer();
    this.clearPingTimer();
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private clearPingTimer(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  private updateConnectionState(updates: Partial<ConnectionState>): void {
    this.connectionState = { ...this.connectionState, ...updates };
    
    // Notify all connection handlers
    this.connectionHandlers.forEach(handler => {
      try {
        handler(this.connectionState);
      } catch (error) {
        console.error('[WebSocket] Connection handler error:', error);
      }
    });
  }

  // ============================================================================
  // GETTERS
  // ============================================================================

  get isConnected(): boolean {
    return this.connectionState.isConnected;
  }

  get isConnecting(): boolean {
    return this.connectionState.isConnecting;
  }

  get connectionError(): string | null {
    return this.connectionState.error;
  }

  get state(): ConnectionState {
    return { ...this.connectionState };
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const webSocketService = new WebSocketService();
