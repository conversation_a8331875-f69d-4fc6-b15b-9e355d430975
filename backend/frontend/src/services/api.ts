/**
 * API Service - Validated Against Backend Implementation
 * 
 * This service uses the exact API endpoints confirmed during our
 * comprehensive backend validation testing.
 */

import axios, { AxiosInstance, AxiosError } from 'axios';
import type {
  SystemStatus,
  DataCollectionStatus,
  PatternDetectionStatus,
  RealtimeStatus,
  SymbolsResponse,
  MarketDataResponse,
  PatternAnalysisResponse,
  RecentPatternsResponse,
  ApiError,
} from '../types/api';

// ============================================================================
// API CONFIGURATION (Validated endpoints)
// ============================================================================

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Validated API endpoints from our testing
export const API_ENDPOINTS = {
  // System endpoints
  HEALTH: '/health',
  STATUS: '/api/v1/status',
  
  // Data collection endpoints
  DATA_COLLECTION_STATUS: '/api/v1/data/collection-status',
  DATA_START_COLLECTION: '/api/v1/data/start-collection',
  DATA_STOP_COLLECTION: '/api/v1/data/stop-collection',
  DATA_SYMBOLS: '/api/v1/symbols',
  DATA_MARKET: '/api/v1/data/market',
  
  // Pattern detection endpoints
  PATTERNS_STATUS: '/api/v1/patterns/status',
  PATTERNS_START: '/api/v1/patterns/start',
  PATTERNS_STOP: '/api/v1/patterns/stop',
  PATTERNS_RECENT: '/api/v1/patterns/recent',
  PATTERNS_ANALYZE: '/api/v1/patterns/analyze',
  PATTERNS_CONFIG_CONFIDENCE: '/api/v1/patterns/config/confidence',
  
  // Real-time endpoints
  REALTIME_STATUS: '/api/v1/realtime/status',
  REALTIME_START: '/api/v1/realtime/start',
  REALTIME_STOP: '/api/v1/realtime/stop',
  REALTIME_BROADCAST_TEST: '/api/v1/realtime/broadcast/test',
  
  // WebSocket endpoints
  WS_STATS: '/ws/stats',
} as const;

// ============================================================================
// AXIOS INSTANCE CONFIGURATION
// ============================================================================

class ApiService {
  private readonly client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000, // 10 second timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[API] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[API] ${response.status} ${response.config.url}`);
        return response;
      },
      (error: AxiosError) => {
        const apiError = this.handleApiError(error);
        console.error('[API] Response error:', apiError);
        return Promise.reject(apiError);
      }
    );
  }

  private handleApiError(error: AxiosError): ApiError {
    const timestamp = new Date().toISOString();
    
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data?.message || error.message,
        status: error.response.status,
        code: error.code,
        timestamp,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        message: 'Network error - unable to reach server',
        status: 0,
        code: error.code,
        timestamp,
      };
    } else {
      // Something else happened
      return {
        message: error.message,
        code: error.code,
        timestamp,
      };
    }
  }

  // ============================================================================
  // SYSTEM API METHODS (Validated endpoints)
  // ============================================================================

  async getHealth(): Promise<{ status: string }> {
    const response = await this.client.get(API_ENDPOINTS.HEALTH);
    return response.data;
  }

  async getSystemStatus(): Promise<SystemStatus> {
    const response = await this.client.get(API_ENDPOINTS.STATUS);
    return response.data;
  }

  // ============================================================================
  // DATA COLLECTION API METHODS (Validated endpoints)
  // ============================================================================

  async getDataCollectionStatus(): Promise<DataCollectionStatus> {
    const response = await this.client.get(API_ENDPOINTS.DATA_COLLECTION_STATUS);
    return response.data;
  }

  async startDataCollection(): Promise<{ message: string; status: string }> {
    const response = await this.client.post(API_ENDPOINTS.DATA_START_COLLECTION);
    return response.data;
  }

  async stopDataCollection(): Promise<{ message: string; status: string }> {
    const response = await this.client.post(API_ENDPOINTS.DATA_STOP_COLLECTION);
    return response.data;
  }

  async getSymbols(): Promise<SymbolsResponse> {
    const response = await this.client.get(API_ENDPOINTS.DATA_SYMBOLS);
    return response.data;
  }

  async getMarketData(symbol: string, hours: number = 24): Promise<MarketDataResponse> {
    const response = await this.client.get(
      `${API_ENDPOINTS.DATA_MARKET}/${symbol}`,
      { params: { hours } }
    );
    return response.data;
  }

  // ============================================================================
  // PATTERN DETECTION API METHODS (Validated endpoints)
  // ============================================================================

  async getPatternDetectionStatus(): Promise<PatternDetectionStatus> {
    const response = await this.client.get(API_ENDPOINTS.PATTERNS_STATUS);
    return response.data;
  }

  async startPatternDetection(): Promise<{ message: string; status: string }> {
    const response = await this.client.post(API_ENDPOINTS.PATTERNS_START);
    return response.data;
  }

  async stopPatternDetection(): Promise<{ message: string; status: string }> {
    const response = await this.client.post(API_ENDPOINTS.PATTERNS_STOP);
    return response.data;
  }

  async getRecentPatterns(hours: number = 24, symbol?: string): Promise<RecentPatternsResponse> {
    const params: Record<string, string | number> = { hours };
    if (symbol) {
      params.symbol = symbol;
    }
    
    const response = await this.client.get(API_ENDPOINTS.PATTERNS_RECENT, { params });
    return response.data;
  }

  async analyzeSymbolPatterns(symbol: string): Promise<PatternAnalysisResponse> {
    const response = await this.client.post(`${API_ENDPOINTS.PATTERNS_ANALYZE}/${symbol}`);
    return response.data;
  }

  async updateConfidenceThreshold(threshold: number): Promise<{ message: string; threshold: number }> {
    const response = await this.client.put(
      `${API_ENDPOINTS.PATTERNS_CONFIG_CONFIDENCE}?threshold=${threshold}`
    );
    return response.data;
  }

  // ============================================================================
  // REAL-TIME API METHODS (Validated endpoints)
  // ============================================================================

  async getRealtimeStatus(): Promise<RealtimeStatus> {
    const response = await this.client.get(API_ENDPOINTS.REALTIME_STATUS);
    return response.data;
  }

  async startRealtimeBroadcasting(): Promise<{ message: string; status: string }> {
    const response = await this.client.post(API_ENDPOINTS.REALTIME_START);
    return response.data;
  }

  async stopRealtimeBroadcasting(): Promise<{ message: string; status: string }> {
    const response = await this.client.post(API_ENDPOINTS.REALTIME_STOP);
    return response.data;
  }

  async sendTestBroadcast(): Promise<{ message: string; recipients: number; test_message: unknown }> {
    const response = await this.client.post(API_ENDPOINTS.REALTIME_BROADCAST_TEST);
    return response.data;
  }

  async getWebSocketStats(): Promise<{
    total_connections: number;
    active_clients: readonly string[];
    connection_info: Record<string, { connected_at: string; last_ping: string }>;
  }> {
    const response = await this.client.get(API_ENDPOINTS.WS_STATS);
    return response.data;
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const apiService = new ApiService();

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

export const api = {
  // System
  health: () => apiService.getHealth(),
  status: () => apiService.getSystemStatus(),
  
  // Data Collection
  dataStatus: () => apiService.getDataCollectionStatus(),
  startData: () => apiService.startDataCollection(),
  stopData: () => apiService.stopDataCollection(),
  symbols: () => apiService.getSymbols(),
  marketData: (symbol: string, hours?: number) => apiService.getMarketData(symbol, hours),
  
  // Pattern Detection
  patternStatus: () => apiService.getPatternDetectionStatus(),
  startPatterns: () => apiService.startPatternDetection(),
  stopPatterns: () => apiService.stopPatternDetection(),
  recentPatterns: (hours?: number, symbol?: string) => apiService.getRecentPatterns(hours, symbol),
  analyzeSymbol: (symbol: string) => apiService.analyzeSymbolPatterns(symbol),
  updateConfidence: (threshold: number) => apiService.updateConfidenceThreshold(threshold),
  
  // Real-time
  realtimeStatus: () => apiService.getRealtimeStatus(),
  startRealtime: () => apiService.startRealtimeBroadcasting(),
  stopRealtime: () => apiService.stopRealtimeBroadcasting(),
  testBroadcast: () => apiService.sendTestBroadcast(),
  wsStats: () => apiService.getWebSocketStats(),
} as const;
