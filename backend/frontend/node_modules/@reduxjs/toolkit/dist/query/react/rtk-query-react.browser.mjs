import{build<PERSON><PERSON><PERSON><PERSON> as qe,coreModule as Ke}from"@reduxjs/toolkit/query";import"@reduxjs/toolkit";import{batch as be,useDispatch as Ee,useSelector as ke,useStore as Me}from"react-redux";import{createSelector as Oe}from"reselect";function q(e){return e.replace(e[0],e[0].toUpperCase())}function ie(e){return e.type==="query"}function se(e){return e.type==="mutation"}function _(e){return e.type==="infinitequery"}function L(e,...c){return Object.assign(e,...c)}import{formatProdErrorMessage as xe}from"@reduxjs/toolkit";import{defaultSerializeQueryArgs as ue,QueryStatus as me,skipToken as b}from"@reduxjs/toolkit/query";import{useCallback as F,useDebugValue as ee,useEffect as N,useLayoutEffect as Se,useMemo as A,useRef as C,useState as ye}from"react";import{shallowEqual as te}from"react-redux";var K=Symbol();import{useEffect as le,useRef as ge,useMemo as Re}from"react";function j(e,c,x,l){let h=Re(()=>({queryArgs:e,serialized:typeof e=="object"?c({queryArgs:e,endpointDefinition:x,endpointName:l}):e}),[e,c,x,l]),g=ge(h);return le(()=>{g.current.serialized!==h.serialized&&(g.current=h)},[h]),g.current.serialized===h.serialized?g.current.queryArgs:e}import{useEffect as Te,useRef as De}from"react";import{shallowEqual as ae}from"react-redux";function V(e){let c=De(e);return Te(()=>{ae(c.current,e)||(c.current=e)},[e]),ae(c.current,e)?c.current:e}var Ae=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Be=Ae(),Pe=()=>typeof navigator<"u"&&navigator.product==="ReactNative",he=Pe(),Ie=()=>Be||he?Se:N,Ue=Ie(),oe=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:me.pending}:e;function ne(e,...c){let x={};return c.forEach(l=>{x[l]=e[l]}),x}var re=["data","status","isLoading","isSuccess","isError","error"];function pe({api:e,moduleOptions:{batch:c,hooks:{useDispatch:x,useSelector:l,useStore:h},unstable__sideEffectsInRender:g,createSelector:w},serializeQueryArgs:I,context:B}){let E=g?t=>t():N;return{buildQueryHooks:H,buildInfiniteQueryHooks:Y,buildMutationHook:J,usePrefetch:Z};function $(t,i,p){if(i?.endpointName&&t.isUninitialized){let{endpointName:a}=i,y=B.endpointDefinitions[a];p!==b&&I({queryArgs:i.originalArgs,endpointDefinition:y,endpointName:a})===I({queryArgs:p,endpointDefinition:y,endpointName:a})&&(i=void 0)}let u=t.isSuccess?t.data:i?.data;u===void 0&&(u=t.data);let s=u!==void 0,n=t.isLoading,r=(!i||i.isLoading||i.isUninitialized)&&!s&&n,o=t.isSuccess||s&&(n&&!i?.isError||t.isUninitialized);return{...t,data:u,currentData:t.data,isFetching:n,isLoading:r,isSuccess:o}}function G(t,i,p){if(i?.endpointName&&t.isUninitialized){let{endpointName:a}=i,y=B.endpointDefinitions[a];p!==b&&I({queryArgs:i.originalArgs,endpointDefinition:y,endpointName:a})===I({queryArgs:p,endpointDefinition:y,endpointName:a})&&(i=void 0)}let u=t.isSuccess?t.data:i?.data;u===void 0&&(u=t.data);let s=u!==void 0,n=t.isLoading,r=(!i||i.isLoading||i.isUninitialized)&&!s&&n,o=t.isSuccess||n&&s;return{...t,data:u,currentData:t.data,isFetching:n,isLoading:r,isSuccess:o}}function Z(t,i){let p=x(),u=V(i);return F((s,n)=>p(e.util.prefetch(t,s,{...u,...n})),[t,p,u])}function m(t,i,{refetchOnReconnect:p,refetchOnFocus:u,refetchOnMountOrArgChange:s,skip:n=!1,pollingInterval:r=0,skipPollingIfUnfocused:o=!1,...a}={}){let{initiate:y}=e.endpoints[t],Q=x(),S=C(void 0);if(!S.current){let O=Q(e.internalActions.internal_getRTKQSubscriptions());S.current=O}let f=j(n?b:i,ue,B.endpointDefinitions[t],t),d=V({refetchOnReconnect:p,refetchOnFocus:u,pollingInterval:r,skipPollingIfUnfocused:o}),R=a.initialPageParam,T=V(R),D=C(void 0),{queryCacheKey:U,requestId:M}=D.current||{},z=!1;U&&M&&(z=S.current.isRequestSubscribed(U,M));let X=!z&&D.current!==void 0;return E(()=>{X&&(D.current=void 0)},[X]),E(()=>{let O=D.current;if(f===b){O?.unsubscribe(),D.current=void 0;return}let de=D.current?.subscriptionOptions;if(!O||O.arg!==f){O?.unsubscribe();let ce=Q(y(f,{subscriptionOptions:d,forceRefetch:s,..._(B.endpointDefinitions[t])?{initialPageParam:T}:{}}));D.current=ce}else d!==de&&O.updateSubscriptionOptions(d)},[Q,y,s,f,d,X,T,t]),[D,Q,y,d]}function v(t,i){return(u,{skip:s=!1,selectFromResult:n}={})=>{let{select:r}=e.endpoints[t],o=j(s?b:u,I,B.endpointDefinitions[t],t),a=C(void 0),y=A(()=>w([r(o),(R,T)=>T,R=>o],i,{memoizeOptions:{resultEqualityCheck:te}}),[r,o]),Q=A(()=>n?w([y],n,{devModeChecks:{identityFunctionCheck:"never"}}):y,[y,n]),S=l(R=>Q(R,a.current),te),f=h(),d=y(f.getState(),a.current);return Ue(()=>{a.current=d},[d]),S}}function P(t){N(()=>()=>{t.current?.unsubscribe?.(),t.current=void 0},[t])}function k(t){if(!t.current)throw new Error(xe(38));return t.current.refetch()}function H(t){let i=(s,n={})=>{let[r]=m(t,s,n);return P(r),A(()=>({refetch:()=>k(r)}),[r])},p=({refetchOnReconnect:s,refetchOnFocus:n,pollingInterval:r=0,skipPollingIfUnfocused:o=!1}={})=>{let{initiate:a}=e.endpoints[t],y=x(),[Q,S]=ye(K),f=C(void 0),d=V({refetchOnReconnect:s,refetchOnFocus:n,pollingInterval:r,skipPollingIfUnfocused:o});E(()=>{let U=f.current?.subscriptionOptions;d!==U&&f.current?.updateSubscriptionOptions(d)},[d]);let R=C(d);E(()=>{R.current=d},[d]);let T=F(function(U,M=!1){let z;return c(()=>{f.current?.unsubscribe(),f.current=z=y(a(U,{subscriptionOptions:R.current,forceRefetch:!M})),S(U)}),z},[y,a]),D=F(()=>{f.current?.queryCacheKey&&y(e.internalActions.removeQueryResult({queryCacheKey:f.current?.queryCacheKey}))},[y]);return N(()=>()=>{f?.current?.unsubscribe()},[]),N(()=>{Q!==K&&!f.current&&T(Q,!0)},[Q,T]),A(()=>[T,Q,{reset:D}],[T,Q,D])},u=v(t,$);return{useQueryState:u,useQuerySubscription:i,useLazyQuerySubscription:p,useLazyQuery(s){let[n,r,{reset:o}]=p(s),a=u(r,{...s,skip:r===K}),y=A(()=>({lastArg:r}),[r]);return A(()=>[n,{...a,reset:o},y],[n,a,o,y])},useQuery(s,n){let r=i(s,n),o=u(s,{selectFromResult:s===b||n?.skip?void 0:oe,...n}),a=ne(o,...re);return ee(a),A(()=>({...o,...r}),[o,r])}}}function Y(t){let i=(u,s={})=>{let[n,r,o,a]=m(t,u,s),y=C(a);E(()=>{y.current=a},[a]);let Q=F(function(d,R){let T;return c(()=>{n.current?.unsubscribe(),n.current=T=r(o(d,{subscriptionOptions:y.current,direction:R}))}),T},[n,r,o]);P(n);let S=j(s.skip?b:u,ue,B.endpointDefinitions[t],t),f=F(()=>k(n),[n]);return A(()=>({trigger:Q,refetch:f,fetchNextPage:()=>Q(S,"forward"),fetchPreviousPage:()=>Q(S,"backward")}),[f,Q,S])},p=v(t,G);return{useInfiniteQueryState:p,useInfiniteQuerySubscription:i,useInfiniteQuery(u,s){let{refetch:n,fetchNextPage:r,fetchPreviousPage:o}=i(u,s),a=p(u,{selectFromResult:u===b||s?.skip?void 0:oe,...s}),y=ne(a,...re,"hasNextPage","hasPreviousPage");return ee(y),A(()=>({...a,fetchNextPage:r,fetchPreviousPage:o,refetch:n}),[a,r,o,n])}}}function J(t){return({selectFromResult:i,fixedCacheKey:p}={})=>{let{select:u,initiate:s}=e.endpoints[t],n=x(),[r,o]=ye();N(()=>()=>{r?.arg.fixedCacheKey||r?.reset()},[r]);let a=F(function(U){let M=n(s(U,{fixedCacheKey:p}));return o(M),M},[n,s,p]),{requestId:y}=r||{},Q=A(()=>u({fixedCacheKey:p,requestId:r?.requestId}),[p,r,u]),S=A(()=>i?w([Q],i):Q,[i,Q]),f=l(S,te),d=p==null?r?.arg.originalArgs:void 0,R=F(()=>{c(()=>{r&&o(void 0),p&&n(e.internalActions.removeMutationResult({requestId:y,fixedCacheKey:p}))})},[n,p,r,y]),T=ne(f,...re,"endpointName");ee(T);let D=A(()=>({...f,originalArgs:d,reset:R}),[f,d,R]);return A(()=>[a,D],[a,D])}}}var fe=Symbol(),Qe=({batch:e=be,hooks:c={useDispatch:Ee,useSelector:ke,useStore:Me},createSelector:x=Oe,unstable__sideEffectsInRender:l=!1,...h}={})=>({name:fe,init(g,{serializeQueryArgs:w},I){let B=g,{buildQueryHooks:E,buildInfiniteQueryHooks:$,buildMutationHook:G,usePrefetch:Z}=pe({api:g,moduleOptions:{batch:e,hooks:c,unstable__sideEffectsInRender:l,createSelector:x},serializeQueryArgs:w,context:I});return L(B,{usePrefetch:Z}),L(I,{batch:e}),{injectEndpoint(m,v){if(ie(v)){let{useQuery:P,useLazyQuery:k,useLazyQuerySubscription:H,useQueryState:Y,useQuerySubscription:J}=E(m);L(B.endpoints[m],{useQuery:P,useLazyQuery:k,useLazyQuerySubscription:H,useQueryState:Y,useQuerySubscription:J}),g[`use${q(m)}Query`]=P,g[`useLazy${q(m)}Query`]=k}if(se(v)){let P=G(m);L(B.endpoints[m],{useMutation:P}),g[`use${q(m)}Mutation`]=P}else if(_(v)){let{useInfiniteQuery:P,useInfiniteQuerySubscription:k,useInfiniteQueryState:H}=$(m);L(B.endpoints[m],{useInfiniteQuery:P,useInfiniteQuerySubscription:k,useInfiniteQueryState:H}),g[`use${q(m)}InfiniteQuery`]=P}}}}});export*from"@reduxjs/toolkit/query";import{configureStore as Fe,formatProdErrorMessage as we}from"@reduxjs/toolkit";import{useContext as ve}from"react";import{useEffect as Le}from"react";import*as W from"react";import{Provider as Ce,ReactReduxContext as Ne}from"react-redux";import{setupListeners as He}from"@reduxjs/toolkit/query";function ze(e){let c=e.context||Ne;if(ve(c))throw new Error(we(35));let[l]=W.useState(()=>Fe({reducer:{[e.api.reducerPath]:e.api.reducer},middleware:h=>h().concat(e.api.middleware)}));return Le(()=>e.setupListeners===!1?void 0:He(l.dispatch,e.setupListeners),[e.setupListeners,l.dispatch]),W.createElement(Ce,{store:l,context:c},e.children)}var It=qe(Ke(),Qe());export{ze as ApiProvider,K as UNINITIALIZED_VALUE,It as createApi,Qe as reactHooksModule,fe as reactHooksModuleName};
//# sourceMappingURL=rtk-query-react.browser.mjs.map