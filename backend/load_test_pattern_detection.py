#!/usr/bin/env python3
"""
Enterprise-Grade Pattern Detection Load Testing

Tests pattern detection performance under load:
- Multiple simultaneous pattern triggers
- Pattern detection accuracy under stress
- Memory usage during intensive analysis
- Response time consistency
"""

import asyncio
import sys
import os
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import random

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.pattern_detection.base import CorrelationWindow
from app.pattern_detection.cex_listing import CEXListingDetector, WhaleAccumulationDetector

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PatternDetectionLoadTester:
    """Enterprise-grade pattern detection load tester"""
    
    def __init__(self):
        self.cex_detector = CEXListingDetector(min_confidence=0.6)
        self.whale_detector = WhaleAccumulationDetector(min_confidence=0.6)
        self.test_results = []
        self.performance_metrics = []
        
    async def run_load_test(self) -> bool:
        """Run comprehensive pattern detection load test"""
        logger.info("🚀 Starting Pattern Detection Load Test")
        
        try:
            # Phase 1: Single pattern detection performance
            logger.info("Phase 1: Single pattern detection performance...")
            single_success = await self._single_pattern_performance_test()
            
            # Phase 2: Concurrent pattern detection
            logger.info("Phase 2: Concurrent pattern detection...")
            concurrent_success = await self._concurrent_pattern_test()
            
            # Phase 3: Stress test with many symbols
            logger.info("Phase 3: Stress test with many symbols...")
            stress_success = await self._stress_test_many_symbols()
            
            # Phase 4: Accuracy validation under load
            logger.info("Phase 4: Accuracy validation under load...")
            accuracy_success = await self._accuracy_validation_test()
            
            # Generate report
            self._generate_pattern_report()
            
            return single_success and concurrent_success and stress_success and accuracy_success
            
        except Exception as e:
            logger.error(f"Pattern detection load test failed: {e}")
            return False
    
    async def _single_pattern_performance_test(self) -> bool:
        """Test single pattern detection performance"""
        logger.info("Testing single pattern detection performance...")
        
        # Test CEX listing detection
        cex_times = []
        for i in range(100):
            window = self._create_cex_test_window(f"TEST{i}")
            
            start_time = time.time()
            pattern = await self.cex_detector.detect(window)
            detection_time = time.time() - start_time
            
            cex_times.append(detection_time)
            
            if pattern:
                self.test_results.append({
                    'type': 'cex_listing',
                    'symbol': f"TEST{i}",
                    'confidence': pattern.confidence,
                    'detection_time': detection_time
                })
        
        # Test whale accumulation detection
        whale_times = []
        for i in range(100):
            window = self._create_whale_test_window(f"WHALE{i}")
            
            start_time = time.time()
            pattern = await self.whale_detector.detect(window)
            detection_time = time.time() - start_time
            
            whale_times.append(detection_time)
            
            if pattern:
                self.test_results.append({
                    'type': 'whale_accumulation',
                    'symbol': f"WHALE{i}",
                    'confidence': pattern.confidence,
                    'detection_time': detection_time
                })
        
        avg_cex_time = sum(cex_times) / len(cex_times)
        avg_whale_time = sum(whale_times) / len(whale_times)
        
        logger.info(f"CEX detection average time: {avg_cex_time:.4f}s")
        logger.info(f"Whale detection average time: {avg_whale_time:.4f}s")
        
        # Success criteria: <100ms average detection time
        return avg_cex_time < 0.1 and avg_whale_time < 0.1
    
    async def _concurrent_pattern_test(self) -> bool:
        """Test concurrent pattern detection"""
        logger.info("Testing concurrent pattern detection...")
        
        # Create multiple detection tasks
        detection_tasks = []
        
        for i in range(50):
            # Mix of CEX and whale patterns
            if i % 2 == 0:
                window = self._create_cex_test_window(f"CONCURRENT_CEX_{i}")
                task = asyncio.create_task(self._timed_detection(self.cex_detector, window, f"CEX_{i}"))
            else:
                window = self._create_whale_test_window(f"CONCURRENT_WHALE_{i}")
                task = asyncio.create_task(self._timed_detection(self.whale_detector, window, f"WHALE_{i}"))
            
            detection_tasks.append(task)
        
        # Run all detections concurrently
        start_time = time.time()
        results = await asyncio.gather(*detection_tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        successful_detections = sum(1 for r in results if r is not None and not isinstance(r, Exception))
        
        logger.info(f"Concurrent detection completed:")
        logger.info(f"  Total time: {total_time:.2f}s")
        logger.info(f"  Successful detections: {successful_detections}/50")
        logger.info(f"  Average time per detection: {total_time/50:.4f}s")
        
        # Success criteria: >90% success rate, <5s total time
        success_rate = successful_detections / 50
        return success_rate > 0.9 and total_time < 5.0
    
    async def _stress_test_many_symbols(self) -> bool:
        """Stress test with many symbols"""
        logger.info("Stress testing with 200 symbols...")
        
        # Create 200 test windows
        test_windows = []
        for i in range(200):
            if i % 3 == 0:
                window = self._create_cex_test_window(f"STRESS_CEX_{i}")
                test_windows.append(('cex', window))
            elif i % 3 == 1:
                window = self._create_whale_test_window(f"STRESS_WHALE_{i}")
                test_windows.append(('whale', window))
            else:
                # Create normal market data (should not trigger patterns)
                window = self._create_normal_test_window(f"STRESS_NORMAL_{i}")
                test_windows.append(('normal', window))
        
        # Process in batches to avoid overwhelming the system
        batch_size = 20
        total_processed = 0
        total_patterns = 0
        
        start_time = time.time()
        
        for i in range(0, len(test_windows), batch_size):
            batch = test_windows[i:i+batch_size]
            batch_tasks = []
            
            for window_type, window in batch:
                if window_type == 'cex':
                    task = asyncio.create_task(self.cex_detector.detect(window))
                elif window_type == 'whale':
                    task = asyncio.create_task(self.whale_detector.detect(window))
                else:
                    # Test both detectors on normal data (should not detect patterns)
                    task = asyncio.create_task(self.cex_detector.detect(window))
                
                batch_tasks.append(task)
            
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            for result in batch_results:
                total_processed += 1
                if result and not isinstance(result, Exception):
                    total_patterns += 1
        
        total_time = time.time() - start_time
        
        logger.info(f"Stress test completed:")
        logger.info(f"  Total symbols processed: {total_processed}")
        logger.info(f"  Patterns detected: {total_patterns}")
        logger.info(f"  Total time: {total_time:.2f}s")
        logger.info(f"  Processing rate: {total_processed/total_time:.1f} symbols/second")
        
        # Success criteria: >50 symbols/second processing rate
        processing_rate = total_processed / total_time
        return processing_rate > 50
    
    async def _accuracy_validation_test(self) -> bool:
        """Validate pattern detection accuracy under load"""
        logger.info("Validating pattern detection accuracy...")
        
        # Create known positive and negative cases
        positive_cases = []
        negative_cases = []
        
        # 50 positive CEX cases
        for i in range(50):
            window = self._create_cex_test_window(f"POS_CEX_{i}")
            positive_cases.append(('cex', window))
        
        # 50 positive whale cases
        for i in range(50):
            window = self._create_whale_test_window(f"POS_WHALE_{i}")
            positive_cases.append(('whale', window))
        
        # 100 negative cases (normal market data)
        for i in range(100):
            window = self._create_normal_test_window(f"NEG_{i}")
            negative_cases.append(('normal', window))
        
        # Test positive cases
        true_positives = 0
        for case_type, window in positive_cases:
            if case_type == 'cex':
                result = await self.cex_detector.detect(window)
            else:
                result = await self.whale_detector.detect(window)
            
            if result and result.confidence >= 0.6:
                true_positives += 1
        
        # Test negative cases
        false_positives = 0
        for case_type, window in negative_cases:
            cex_result = await self.cex_detector.detect(window)
            whale_result = await self.whale_detector.detect(window)
            
            if (cex_result and cex_result.confidence >= 0.6) or \
               (whale_result and whale_result.confidence >= 0.6):
                false_positives += 1
        
        sensitivity = true_positives / len(positive_cases)  # True positive rate
        specificity = (len(negative_cases) - false_positives) / len(negative_cases)  # True negative rate
        
        logger.info(f"Accuracy validation results:")
        logger.info(f"  Sensitivity (true positive rate): {sensitivity:.2%}")
        logger.info(f"  Specificity (true negative rate): {specificity:.2%}")
        logger.info(f"  False positive rate: {false_positives/len(negative_cases):.2%}")
        
        # Success criteria: >80% sensitivity, >90% specificity
        return sensitivity > 0.8 and specificity > 0.9
    
    async def _timed_detection(self, detector, window, symbol):
        """Run pattern detection with timing"""
        start_time = time.time()
        try:
            result = await detector.detect(window)
            detection_time = time.time() - start_time
            
            self.performance_metrics.append({
                'symbol': symbol,
                'detection_time': detection_time,
                'success': result is not None
            })
            
            return result
        except Exception as e:
            logger.error(f"Detection failed for {symbol}: {e}")
            return None
    
    def _create_cex_test_window(self, symbol: str) -> CorrelationWindow:
        """Create test window that should trigger CEX listing detection"""
        now = datetime.now()
        
        market_data = [
            {
                'timestamp': now - timedelta(minutes=120),
                'price': 100.0,
                'volume': 1000000,
            },
            {
                'timestamp': now - timedelta(minutes=90),
                'price': 102.0,
                'volume': 1100000,
            },
            {
                'timestamp': now - timedelta(minutes=60),
                'price': 105.0,
                'volume': 1200000,
            },
            {
                'timestamp': now - timedelta(minutes=30),
                'price': 125.0,
                'volume': 1300000,
            },
            {
                'timestamp': now,
                'price': 130.0,  # 24% increase
                'volume': 6000000,  # 5x volume spike
            }
        ]
        
        social_mentions = [
            {
                'timestamp': now - timedelta(minutes=45),
                'platform': 'reddit',
                'content': f'{symbol} token is getting listed on Binance tomorrow!',
                'engagement_score': 85.0,
                'sentiment_score': 0.8
            },
            {
                'timestamp': now - timedelta(minutes=30),
                'platform': 'reddit',
                'content': f'Binance just announced {symbol} listing, huge news!',
                'engagement_score': 120.0,
                'sentiment_score': 0.9
            }
        ]
        
        return CorrelationWindow(
            symbol=symbol,
            start_time=now - timedelta(minutes=120),
            end_time=now,
            market_data=market_data,
            events=[],
            social_mentions=social_mentions
        )
    
    def _create_whale_test_window(self, symbol: str) -> CorrelationWindow:
        """Create test window that should trigger whale accumulation detection"""
        now = datetime.now()
        
        market_data = [
            {
                'timestamp': now - timedelta(minutes=300),
                'price': 50.0,
                'volume': 800000,
            },
            {
                'timestamp': now - timedelta(minutes=240),
                'price': 50.1,
                'volume': 900000,
            },
            {
                'timestamp': now - timedelta(minutes=180),
                'price': 49.9,
                'volume': 1000000,
            },
            {
                'timestamp': now - timedelta(minutes=120),
                'price': 50.0,
                'volume': 1100000,
            },
            {
                'timestamp': now - timedelta(minutes=60),
                'price': 50.2,
                'volume': 1200000,
            },
            {
                'timestamp': now,
                'price': 50.1,  # Very stable price
                'volume': 2400000,  # 2.4x volume increase
            }
        ]
        
        return CorrelationWindow(
            symbol=symbol,
            start_time=now - timedelta(minutes=300),
            end_time=now,
            market_data=market_data,
            events=[],
            social_mentions=[]
        )
    
    def _create_normal_test_window(self, symbol: str) -> CorrelationWindow:
        """Create test window with normal market data (should not trigger patterns)"""
        now = datetime.now()
        
        market_data = [
            {
                'timestamp': now - timedelta(minutes=60),
                'price': 100.0,
                'volume': 1000000,
            },
            {
                'timestamp': now - timedelta(minutes=30),
                'price': 102.0,  # Only 2% increase
                'volume': 1100000,  # Only 10% volume increase
            },
            {
                'timestamp': now,
                'price': 101.5,
                'volume': 950000,
            }
        ]
        
        return CorrelationWindow(
            symbol=symbol,
            start_time=now - timedelta(minutes=60),
            end_time=now,
            market_data=market_data,
            events=[],
            social_mentions=[]
        )
    
    def _generate_pattern_report(self):
        """Generate comprehensive pattern detection report"""
        logger.info("\n" + "="*60)
        logger.info("PATTERN DETECTION LOAD TEST REPORT")
        logger.info("="*60)
        
        if self.test_results:
            cex_results = [r for r in self.test_results if r['type'] == 'cex_listing']
            whale_results = [r for r in self.test_results if r['type'] == 'whale_accumulation']
            
            logger.info(f"CEX Listing Patterns Detected: {len(cex_results)}")
            logger.info(f"Whale Accumulation Patterns Detected: {len(whale_results)}")
            
            if cex_results:
                avg_cex_confidence = sum(r['confidence'] for r in cex_results) / len(cex_results)
                logger.info(f"Average CEX Confidence: {avg_cex_confidence:.3f}")
            
            if whale_results:
                avg_whale_confidence = sum(r['confidence'] for r in whale_results) / len(whale_results)
                logger.info(f"Average Whale Confidence: {avg_whale_confidence:.3f}")
        
        if self.performance_metrics:
            avg_time = sum(m['detection_time'] for m in self.performance_metrics) / len(self.performance_metrics)
            max_time = max(m['detection_time'] for m in self.performance_metrics)
            success_rate = sum(1 for m in self.performance_metrics if m['success']) / len(self.performance_metrics)
            
            logger.info(f"Average Detection Time: {avg_time:.4f}s")
            logger.info(f"Maximum Detection Time: {max_time:.4f}s")
            logger.info(f"Success Rate: {success_rate:.2%}")
        
        logger.info("="*60)


async def main():
    """Run pattern detection load test"""
    tester = PatternDetectionLoadTester()
    
    try:
        success = await tester.run_load_test()
        return 0 if success else 1
    
    except KeyboardInterrupt:
        logger.info("Load test interrupted by user")
        return 1
    
    except Exception as e:
        logger.error(f"Load test failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
