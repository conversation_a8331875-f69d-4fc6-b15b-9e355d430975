# Nexus - Crypto Causal Event Correlation Engine

## Overview
Nexus is a real-time causal inference engine that automatically identifies and surfaces the specific on-chain and off-chain events that are the most probable cause for significant price or volume movements in crypto assets.

## Architecture
- **Backend**: FastAPI + Python
- **Frontend**: Next.js 15
- **Database**: PostgreSQL + TimescaleDB
- **Cache/Queue**: Redis
- **Deployment**: Docker + Single VPS

## Budget Constraints
- Maximum: $100/month
- Target: $28/month (VPS + domain)
- APIs: Free tiers only

## Development Phases
- **Week 1**: Foundation & Data Pipeline
- **Week 2**: Pattern Detection Engine  
- **Week 3**: API & Frontend
- **Week 4**: Production & Polish

## Quick Start
```bash
# Clone and setup
git clone <repo>
cd nexus
cp .env.example .env
# Edit .env with your API keys

# Start development environment
docker-compose up -d

# Access application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# Grafana: http://localhost:3001
```

## Current Status
🚧 **Week 1 - Day 1**: Environment Setup in Progress
