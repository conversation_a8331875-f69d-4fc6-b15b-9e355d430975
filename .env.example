# Nexus Environment Configuration

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql://nexus_user:nexus_password@localhost:5432/nexus_db
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# FREE API KEYS (Required)
# =============================================================================

# CoinGecko API (Market data, new listings)
# Free tier: 50 calls/minute
# Get key at: https://www.coingecko.com/en/api/pricing
COINGECKO_API_KEY=your_coingecko_api_key_here

# Reddit API (Social sentiment)
# Free tier: 100 calls/minute
# Get credentials at: https://www.reddit.com/prefs/apps
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=nexus-bot/1.0

# Twitter API v2 (Academic Research - Optional)
# Free tier: 10M tweets/month (requires approval)
# Apply at: https://developer.twitter.com/en/products/twitter-api/academic-research
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here

# GitHub API (Development activity tracking)
# Free tier: 5000 calls/hour
# Get token at: https://github.com/settings/tokens
GITHUB_TOKEN=your_github_token_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
DEBUG=true
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-change-in-production

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
FRONTEND_URL=http://localhost:3000

# Background Jobs
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
GRAFANA_ADMIN_PASSWORD=admin123
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# =============================================================================
# PATTERN DETECTION SETTINGS
# =============================================================================
MIN_CONFIDENCE_THRESHOLD=0.70
CORRELATION_WINDOW_MINUTES=60
MAX_SYMBOLS_TO_MONITOR=50

# =============================================================================
# RATE LIMITING
# =============================================================================
COINGECKO_RATE_LIMIT=45  # calls per minute (buffer for 50 limit)
REDDIT_RATE_LIMIT=90     # calls per minute (buffer for 100 limit)
BINANCE_RECONNECT_DELAY=5  # seconds
