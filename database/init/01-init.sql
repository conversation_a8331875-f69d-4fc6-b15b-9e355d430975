-- Nexus Database Initialization Script
-- This script sets up the basic database structure for the Nexus application

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Create database user if not exists (for local development)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'nexus_user') THEN
        CREATE ROLE nexus_user WITH LOGIN PASSWORD 'nexus_password';
    END IF;
END
$$;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE nexus_db TO nexus_user;
GRANT ALL ON SCHEMA public TO nexus_user;

-- Create basic tables (will be managed by SQLAlchemy/Alembic later)
-- This is just for initial setup

-- Market data table (will be converted to hypertable)
CREATE TABLE IF NOT EXISTS market_data (
    time TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    price DECIMAL(20,8),
    volume DECIMAL(20,8),
    market_cap DECIMAL(20,2),
    source VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Events table (will be converted to hypertable)
CREATE TABLE IF NOT EXISTS events (
    time TIMESTAMPTZ NOT NULL,
    event_id UUID DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL,
    symbol VARCHAR(20),
    data JSONB,
    source VARCHAR(100),
    confidence_score DECIMAL(3,2),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Correlations table
CREATE TABLE IF NOT EXISTS correlations (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    pattern_type VARCHAR(100) NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    trigger_time TIMESTAMPTZ NOT NULL,
    evidence JSONB,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Users table (for future authentication)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    subscription_tier VARCHAR(20) DEFAULT 'free',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Alert configurations
CREATE TABLE IF NOT EXISTS alert_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    symbol VARCHAR(20),
    pattern_types TEXT[],
    min_confidence DECIMAL(3,2) DEFAULT 0.70,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_market_data_time_symbol ON market_data (time DESC, symbol);
CREATE INDEX IF NOT EXISTS idx_events_time_type ON events (time DESC, event_type);
CREATE INDEX IF NOT EXISTS idx_correlations_symbol_time ON correlations (symbol, trigger_time DESC);
CREATE INDEX IF NOT EXISTS idx_correlations_pattern_confidence ON correlations (pattern_type, confidence_score DESC);

-- Grant permissions on tables
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO nexus_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO nexus_user;
