2025-07-03 19:19:42,188 - main - INFO - Starting Nexus application...
2025-07-03 19:19:42,212 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:19:42,213 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:42,214 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:19:42,214 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:42,215 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:19:42,215 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:42,216 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:19:42,216 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:19:42,216 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:19:42,217 - app.core.database - INFO - Database connection successful
2025-07-03 19:19:42,217 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:19:42,217 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:19:42,217 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:19:42,219 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,219 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,221 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,221 - sqlalchemy.engine.Engine - INFO - [cached since 0.002443s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,222 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,222 - sqlalchemy.engine.Engine - INFO - [cached since 0.002994s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,222 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,222 - sqlalchemy.engine.Engine - INFO - [cached since 0.003455s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,223 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,223 - sqlalchemy.engine.Engine - INFO - [cached since 0.004258s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,224 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,224 - sqlalchemy.engine.Engine - INFO - [cached since 0.004964s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,225 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,225 - sqlalchemy.engine.Engine - INFO - [cached since 0.005652s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,226 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE social_mentions (
	time TIMESTAMP WITH TIME ZONE NOT NULL, 
	mention_id UUID NOT NULL, 
	symbol VARCHAR(20), 
	platform VARCHAR(20), 
	account VARCHAR(100), 
	content TEXT, 
	engagement_score NUMERIC(10, 2), 
	sentiment_score NUMERIC(3, 2), 
	source_url TEXT, 
	created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), 
	PRIMARY KEY (time, mention_id)
)


2025-07-03 19:19:42,226 - sqlalchemy.engine.Engine - INFO - [no key 0.00014s] ()
2025-07-03 19:19:42,231 - sqlalchemy.engine.Engine - INFO - CREATE INDEX idx_social_mentions_symbol_time ON social_mentions (symbol, time)
2025-07-03 19:19:42,231 - sqlalchemy.engine.Engine - INFO - [no key 0.00016s] ()
2025-07-03 19:19:42,233 - sqlalchemy.engine.Engine - INFO - CREATE INDEX idx_social_mentions_time_platform ON social_mentions (time, platform)
2025-07-03 19:19:42,233 - sqlalchemy.engine.Engine - INFO - [no key 0.00015s] ()
2025-07-03 19:19:42,235 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE data_sources (
	id UUID NOT NULL, 
	name VARCHAR(50) NOT NULL, 
	source_type VARCHAR(20) NOT NULL, 
	is_active BOOLEAN, 
	last_successful_fetch TIMESTAMP WITH TIME ZONE, 
	last_error TEXT, 
	error_count INTEGER, 
	requests_per_minute INTEGER, 
	current_usage INTEGER, 
	created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), 
	updated_at TIMESTAMP WITH TIME ZONE, 
	PRIMARY KEY (id), 
	UNIQUE (name)
)


2025-07-03 19:19:42,235 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-03 19:19:42,238 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:19:42,238 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:19:42,238 - sqlalchemy.engine.Engine - INFO - [generated in 0.00009s] ()
2025-07-03 19:19:42,241 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:19:42,241 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:19:42,241 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ()
2025-07-03 19:19:42,243 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:19:42,244 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:19:42,244 - sqlalchemy.engine.Engine - INFO - [generated in 0.00009s] ()
2025-07-03 19:19:42,245 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:19:42,245 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:19:42,246 - main - INFO - Database initialized successfully
2025-07-03 19:19:54,939 - main - INFO - Shutting down Nexus application...
2025-07-03 19:19:55,835 - main - INFO - Starting Nexus application...
2025-07-03 19:19:55,860 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:19:55,860 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:55,861 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:19:55,861 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:55,862 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:19:55,862 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:55,863 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:19:55,864 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:19:55,864 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:19:55,865 - app.core.database - INFO - Database connection successful
2025-07-03 19:19:55,865 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:19:55,865 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:19:55,866 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:19:55,868 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,868 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,870 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,871 - sqlalchemy.engine.Engine - INFO - [cached since 0.00285s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,871 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,871 - sqlalchemy.engine.Engine - INFO - [cached since 0.003749s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,872 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,872 - sqlalchemy.engine.Engine - INFO - [cached since 0.004448s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,873 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,873 - sqlalchemy.engine.Engine - INFO - [cached since 0.00525s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,874 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,874 - sqlalchemy.engine.Engine - INFO - [cached since 0.006043s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,874 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,874 - sqlalchemy.engine.Engine - INFO - [cached since 0.006651s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,875 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:19:55,875 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:19:55,875 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:19:55,876 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:19:55,876 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:19:55,877 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:19:55,877 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:19:55,877 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:19:55,877 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:19:55,878 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:19:55,878 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:19:55,879 - main - INFO - Database initialized successfully
2025-07-03 19:20:33,201 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:20:33,205 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:20:33,205 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:20:33,207 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:20:39,835 - app.services.data_collector - INFO - Starting data collection service
2025-07-03 19:20:41,366 - app.services.data_collector - INFO - Monitoring 50 symbols: ['BTC', 'ETH', 'USDC', 'SOL', 'FDUSD', 'XRP', 'PEPE', 'DOGE', 'SUI', 'NEIRO']...
2025-07-03 19:20:41,367 - app.services.data_collector - INFO - Starting Binance WebSocket data collection
2025-07-03 19:20:41,392 - app.services.data_collector - INFO - Starting CoinGecko data collection
2025-07-03 19:20:41,392 - app.data_sources.coingecko_client - ERROR - Error making request to coins/markets: Invalid variable type: value should be str, int or float, got False of type <class 'bool'>
2025-07-03 19:20:41,392 - app.services.data_collector - INFO - Starting health check loop
2025-07-03 19:20:41,393 - app.services.data_collector - WARNING - Binance WebSocket disconnected
2025-07-03 19:20:41,393 - app.services.data_collector - ERROR - Error in CoinGecko data collection: Invalid variable type: value should be str, int or float, got False of type <class 'bool'>
2025-07-03 19:20:41,395 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:20:41,395 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:20:41,395 - sqlalchemy.engine.Engine - INFO - [cached since 8.19s ago] ()
2025-07-03 19:20:41,396 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:20:41,396 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:20:41,418 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:20:41,420 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:20:41,420 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ('coingecko',)
2025-07-03 19:20:41,422 - sqlalchemy.engine.Engine - INFO - INSERT INTO data_sources (id, name, source_type, is_active, last_successful_fetch, last_error, error_count, requests_per_minute, current_usage, updated_at) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::BOOLEAN, $5::TIMESTAMP WITH TIME ZONE, $6::VARCHAR, $7::INTEGER, $8::INTEGER, $9::INTEGER, $10::TIMESTAMP WITH TIME ZONE) RETURNING data_sources.created_at
2025-07-03 19:20:41,422 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] (UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'), 'coingecko', 'api', True, None, "Invalid variable type: value should be str, int or float, got False of type <class 'bool'>", 1, None, 0, None)
2025-07-03 19:20:41,423 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:20:43,267 - app.data_sources.binance_client - INFO - Connected to Binance WebSocket
2025-07-03 19:20:43,269 - app.data_sources.binance_client - INFO - Subscribed to ticker data for 50 symbols
2025-07-03 19:20:51,757 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:20:51,757 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:20:51,758 - sqlalchemy.engine.Engine - INFO - [cached since 18.55s ago] ()
2025-07-03 19:20:51,759 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:21:17,964 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:21:17,965 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:21:17,966 - sqlalchemy.engine.Engine - INFO - [cached since 44.76s ago] ()
2025-07-03 19:21:17,969 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:21:41,401 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:21:41,401 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:21:41,402 - sqlalchemy.engine.Engine - INFO - [cached since 68.2s ago] ()
2025-07-03 19:21:41,403 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:21:41,404 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:21:41,424 - app.data_sources.coingecko_client - ERROR - Error making request to coins/markets: Invalid variable type: value should be str, int or float, got False of type <class 'bool'>
2025-07-03 19:21:41,424 - app.services.data_collector - ERROR - Error in CoinGecko data collection: Invalid variable type: value should be str, int or float, got False of type <class 'bool'>
2025-07-03 19:21:41,426 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:21:41,426 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:21:41,426 - sqlalchemy.engine.Engine - INFO - [cached since 60.01s ago] ('coingecko',)
2025-07-03 19:21:41,428 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET error_count=$1::INTEGER, updated_at=now() WHERE data_sources.id = $2::UUID
2025-07-03 19:21:41,428 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] (2, UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:21:41,429 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:05,632 - main - INFO - Shutting down Nexus application...
2025-07-03 19:22:16,506 - main - INFO - Starting Nexus application...
2025-07-03 19:22:16,529 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:22:16,529 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:16,530 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:22:16,530 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:16,531 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:22:16,531 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:16,532 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:22:16,533 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:22:16,533 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ()
2025-07-03 19:22:16,533 - app.core.database - INFO - Database connection successful
2025-07-03 19:22:16,534 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:22:16,534 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:22:16,534 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:22:16,536 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,536 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,538 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,538 - sqlalchemy.engine.Engine - INFO - [cached since 0.002315s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,539 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,539 - sqlalchemy.engine.Engine - INFO - [cached since 0.002916s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,539 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,539 - sqlalchemy.engine.Engine - INFO - [cached since 0.003619s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,540 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,540 - sqlalchemy.engine.Engine - INFO - [cached since 0.004263s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,540 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,541 - sqlalchemy.engine.Engine - INFO - [cached since 0.004764s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,541 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,541 - sqlalchemy.engine.Engine - INFO - [cached since 0.0053s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,541 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:22:16,542 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:22:16,542 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:16,542 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:22:16,543 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:22:16,543 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:22:16,543 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:22:16,543 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:22:16,544 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:22:16,544 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:22:16,544 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:16,544 - main - INFO - Database initialized successfully
2025-07-03 19:22:19,041 - main - INFO - Shutting down Nexus application...
2025-07-03 19:22:19,559 - main - INFO - Starting Nexus application...
2025-07-03 19:22:19,583 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:22:19,584 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:19,585 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:22:19,585 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:19,586 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:22:19,586 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:19,587 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:22:19,587 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:22:19,587 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:22:19,588 - app.core.database - INFO - Database connection successful
2025-07-03 19:22:19,588 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:22:19,588 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:22:19,588 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:22:19,590 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,590 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,592 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,592 - sqlalchemy.engine.Engine - INFO - [cached since 0.002419s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,593 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,593 - sqlalchemy.engine.Engine - INFO - [cached since 0.002972s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,593 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,594 - sqlalchemy.engine.Engine - INFO - [cached since 0.00349s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,594 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,594 - sqlalchemy.engine.Engine - INFO - [cached since 0.004119s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,595 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,595 - sqlalchemy.engine.Engine - INFO - [cached since 0.004662s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,595 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,595 - sqlalchemy.engine.Engine - INFO - [cached since 0.005318s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,596 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:22:19,596 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:22:19,596 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:22:19,597 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:22:19,597 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:22:19,597 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:19,598 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:22:19,598 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:22:19,598 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:19,598 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:22:19,598 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:19,599 - main - INFO - Database initialized successfully
2025-07-03 19:22:41,065 - main - INFO - Shutting down Nexus application...
2025-07-03 19:22:41,620 - main - INFO - Starting Nexus application...
2025-07-03 19:22:41,643 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:22:41,643 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:41,645 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:22:41,645 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:41,646 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:22:41,646 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:41,647 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:22:41,647 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:22:41,647 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:22:41,648 - app.core.database - INFO - Database connection successful
2025-07-03 19:22:41,648 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:22:41,648 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:22:41,649 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:22:41,651 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,651 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,653 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,653 - sqlalchemy.engine.Engine - INFO - [cached since 0.00278s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,654 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,654 - sqlalchemy.engine.Engine - INFO - [cached since 0.003678s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,655 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,655 - sqlalchemy.engine.Engine - INFO - [cached since 0.004312s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,655 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,656 - sqlalchemy.engine.Engine - INFO - [cached since 0.004946s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,656 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,656 - sqlalchemy.engine.Engine - INFO - [cached since 0.005441s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,656 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,657 - sqlalchemy.engine.Engine - INFO - [cached since 0.005941s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,657 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:22:41,657 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:22:41,657 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:22:41,658 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:22:41,658 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:22:41,658 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:41,659 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:22:41,659 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:22:41,659 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:22:41,659 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:22:41,660 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:41,660 - main - INFO - Database initialized successfully
2025-07-03 19:22:53,252 - main - INFO - Shutting down Nexus application...
2025-07-03 19:22:54,129 - main - INFO - Starting Nexus application...
2025-07-03 19:22:54,152 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:22:54,153 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:54,154 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:22:54,154 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:54,155 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:22:54,155 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:54,156 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:22:54,156 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:22:54,156 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:54,157 - app.core.database - INFO - Database connection successful
2025-07-03 19:22:54,157 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:22:54,157 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ()
2025-07-03 19:22:54,158 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:22:54,160 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,160 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,162 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,162 - sqlalchemy.engine.Engine - INFO - [cached since 0.002477s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,163 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,163 - sqlalchemy.engine.Engine - INFO - [cached since 0.003306s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,163 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,164 - sqlalchemy.engine.Engine - INFO - [cached since 0.003937s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,164 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,164 - sqlalchemy.engine.Engine - INFO - [cached since 0.004598s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,165 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,165 - sqlalchemy.engine.Engine - INFO - [cached since 0.005091s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,165 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,165 - sqlalchemy.engine.Engine - INFO - [cached since 0.00564s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,166 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:22:54,166 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:22:54,166 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:22:54,167 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:22:54,167 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:22:54,167 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:22:54,167 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:22:54,167 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:22:54,168 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:22:54,168 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:22:54,168 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:54,169 - main - INFO - Database initialized successfully
2025-07-03 19:23:02,049 - app.services.data_collector - INFO - Starting data collection service
2025-07-03 19:23:04,015 - app.services.data_collector - INFO - Monitoring 50 symbols: ['BTC', 'ETH', 'USDC', 'SOL', 'FDUSD', 'XRP', 'PEPE', 'DOGE', 'SUI', 'NEIRO']...
2025-07-03 19:23:04,016 - app.services.data_collector - INFO - Starting Binance WebSocket data collection
2025-07-03 19:23:04,036 - app.services.data_collector - INFO - Starting CoinGecko data collection
2025-07-03 19:23:04,037 - app.services.data_collector - INFO - Starting health check loop
2025-07-03 19:23:04,037 - app.services.data_collector - WARNING - Binance WebSocket disconnected
2025-07-03 19:23:04,039 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:23:04,043 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:23:04,044 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ()
2025-07-03 19:23:04,046 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:23:04,046 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:23:04,681 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:23:04,688 - sqlalchemy.engine.Engine - INFO - INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR) ON CONFLICT (time, symbol) DO UPDATE SET price = excluded.price, volume = excluded.volume, market_cap = excluded.market_cap, source = excluded.source
2025-07-03 19:23:04,689 - sqlalchemy.engine.Engine - INFO - [no key 0.00114s] (datetime.datetime(2025, 7, 3, 19, 23, 4, 674913), 'BTC', 109886, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674930), 'ETH', 2591.98, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674932), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674934), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674936), 'BNB', 661.31, None, 96468274517, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674938), 'SOL', 151.85, None, 81176547076, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674940), 'USDC', 0.999904, None, 62062221900, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674942), 'TRX', 0.285818, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674945), 'DOGE' ... 200 parameters truncated ... 3013202774, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675017), 'OKB', 50.03, None, 3003018801, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675019), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675020), 'NEAR', 2.27, None, 2788722437, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675022), 'JITOSOL', 184.23, None, 2708305830, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675023), 'ICP', 5.01, None, 2675746100, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675025), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675026), 'CRO', 0.082944, None, 2579830012, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675028), 'ONDO', 0.796419, None, 2515167110, 'coingecko')
2025-07-03 19:23:04,695 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:23:04,695 - app.services.data_storage - ERROR - Failed to store market data batch: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.InvalidColumnReferenceError'>: there is no unique or exclusion constraint matching the ON CONFLICT specification
[SQL: INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR) ON CONFLICT (time, symbol) DO UPDATE SET price = excluded.price, volume = excluded.volume, market_cap = excluded.market_cap, source = excluded.source]
[parameters: (datetime.datetime(2025, 7, 3, 19, 23, 4, 674913), 'BTC', 109886, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674930), 'ETH', 2591.98, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674932), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674934), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674936), 'BNB', 661.31, None, 96468274517, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674938), 'SOL', 151.85, None, 81176547076, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674940), 'USDC', 0.999904, None, 62062221900, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674942), 'TRX', 0.285818, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674945), 'DOGE' ... 200 parameters truncated ... 3013202774, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675017), 'OKB', 50.03, None, 3003018801, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675019), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675020), 'NEAR', 2.27, None, 2788722437, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675022), 'JITOSOL', 184.23, None, 2708305830, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675023), 'ICP', 5.01, None, 2675746100, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675025), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675026), 'CRO', 0.082944, None, 2579830012, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675028), 'ONDO', 0.796419, None, 2515167110, 'coingecko')]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-03 19:23:04,697 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:23:04,699 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:23:04,700 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] ('coingecko',)
2025-07-03 19:23:04,703 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET last_error=$1::VARCHAR, error_count=$2::INTEGER, updated_at=now() WHERE data_sources.id = $3::UUID
2025-07-03 19:23:04,704 - sqlalchemy.engine.Engine - INFO - [generated in 0.00033s] ('No data stored', 3, UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:23:04,705 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:23:05,705 - app.data_sources.binance_client - INFO - Connected to Binance WebSocket
2025-07-03 19:23:05,706 - app.data_sources.binance_client - INFO - Subscribed to ticker data for 50 symbols
2025-07-03 19:23:37,204 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:23:37,205 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:23:37,205 - sqlalchemy.engine.Engine - INFO - [cached since 33.16s ago] ()
2025-07-03 19:23:37,206 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:24:04,053 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:04,054 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:24:04,054 - sqlalchemy.engine.Engine - INFO - [cached since 60.01s ago] ()
2025-07-03 19:24:04,056 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:24:04,057 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:24:06,552 - main - INFO - Shutting down Nexus application...
2025-07-03 19:24:17,430 - main - INFO - Starting Nexus application...
2025-07-03 19:24:17,452 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:24:17,453 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:17,454 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:24:17,454 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:17,455 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:24:17,455 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:17,455 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:17,456 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:24:17,456 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:24:17,456 - app.core.database - INFO - Database connection successful
2025-07-03 19:24:17,457 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:24:17,457 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:17,457 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:24:17,459 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,459 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,461 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,461 - sqlalchemy.engine.Engine - INFO - [cached since 0.002615s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,462 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,462 - sqlalchemy.engine.Engine - INFO - [cached since 0.003241s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,463 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,463 - sqlalchemy.engine.Engine - INFO - [cached since 0.003859s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,463 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,463 - sqlalchemy.engine.Engine - INFO - [cached since 0.004399s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,464 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,464 - sqlalchemy.engine.Engine - INFO - [cached since 0.005161s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,465 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,465 - sqlalchemy.engine.Engine - INFO - [cached since 0.005799s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,465 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:24:17,465 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:24:17,465 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:17,466 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:24:17,466 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:24:17,466 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:17,467 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:24:17,467 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:24:17,467 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:17,468 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:24:17,468 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:24:17,468 - main - INFO - Database initialized successfully
2025-07-03 19:24:18,817 - main - INFO - Shutting down Nexus application...
2025-07-03 19:24:19,741 - main - INFO - Starting Nexus application...
2025-07-03 19:24:19,770 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:24:19,770 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:19,772 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:24:19,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:19,773 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:24:19,773 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:19,774 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:19,774 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:24:19,774 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:24:19,775 - app.core.database - INFO - Database connection successful
2025-07-03 19:24:19,775 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:24:19,775 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:24:19,776 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:24:19,777 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,778 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,780 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,780 - sqlalchemy.engine.Engine - INFO - [cached since 0.002805s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,781 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,781 - sqlalchemy.engine.Engine - INFO - [cached since 0.003573s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,781 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,782 - sqlalchemy.engine.Engine - INFO - [cached since 0.004225s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,782 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,782 - sqlalchemy.engine.Engine - INFO - [cached since 0.004816s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,783 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,783 - sqlalchemy.engine.Engine - INFO - [cached since 0.005347s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,783 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,783 - sqlalchemy.engine.Engine - INFO - [cached since 0.005867s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,784 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:24:19,784 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:24:19,784 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:19,785 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:24:19,785 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:24:19,785 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:24:19,785 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:24:19,786 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:24:19,786 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ()
2025-07-03 19:24:19,787 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:24:19,787 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:24:19,787 - main - INFO - Database initialized successfully
2025-07-03 19:24:26,073 - app.services.data_collector - INFO - Starting data collection service
2025-07-03 19:24:27,186 - app.services.data_collector - INFO - Monitoring 50 symbols: ['BTC', 'ETH', 'USDC', 'SOL', 'FDUSD', 'XRP', 'PEPE', 'DOGE', 'SUI', 'NEIRO']...
2025-07-03 19:24:27,187 - app.services.data_collector - INFO - Starting Binance WebSocket data collection
2025-07-03 19:24:27,211 - app.services.data_collector - INFO - Starting CoinGecko data collection
2025-07-03 19:24:27,211 - app.services.data_collector - INFO - Starting health check loop
2025-07-03 19:24:27,212 - app.services.data_collector - WARNING - Binance WebSocket disconnected
2025-07-03 19:24:27,214 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:27,218 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:24:27,218 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ()
2025-07-03 19:24:27,221 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:24:27,221 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:24:28,330 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:28,334 - sqlalchemy.engine.Engine - INFO - INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR) ON CONFLICT (time, symbol) DO UPDATE SET price = excluded.price, volume = excluded.volume, market_cap = excluded.market_cap, source = excluded.source
2025-07-03 19:24:28,335 - sqlalchemy.engine.Engine - INFO - [no key 0.00090s] (datetime.datetime(2025, 7, 3, 19, 24, 28, 328144), 'BTC', 109889, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328153), 'ETH', 2591.36, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328154), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328158), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328159), 'BNB', 661.65, None, 96468274517, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328160), 'SOL', 151.87, None, 81176547076, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328160), 'USDC', 0.99991, None, 62062004477, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328161), 'TRX', 0.285814, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328162), 'DOGE' ... 200 parameters truncated ... 3013202774, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328188), 'OKB', 50.03, None, 3001800933, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328188), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328189), 'NEAR', 2.27, None, 2788722437, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328189), 'JITOSOL', 184.28, None, 2708305830, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328190), 'ICP', 5.01, None, 2678580776, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328190), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328191), 'CRO', 0.082957, None, 2581099287, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328191), 'ONDO', 0.796336, None, 2515167110, 'coingecko')
2025-07-03 19:24:28,338 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:24:28,339 - app.services.data_storage - ERROR - Failed to store market data batch: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.InvalidColumnReferenceError'>: there is no unique or exclusion constraint matching the ON CONFLICT specification
[SQL: INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR) ON CONFLICT (time, symbol) DO UPDATE SET price = excluded.price, volume = excluded.volume, market_cap = excluded.market_cap, source = excluded.source]
[parameters: (datetime.datetime(2025, 7, 3, 19, 24, 28, 328144), 'BTC', 109889, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328153), 'ETH', 2591.36, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328154), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328158), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328159), 'BNB', 661.65, None, 96468274517, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328160), 'SOL', 151.87, None, 81176547076, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328160), 'USDC', 0.99991, None, 62062004477, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328161), 'TRX', 0.285814, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328162), 'DOGE' ... 200 parameters truncated ... 3013202774, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328188), 'OKB', 50.03, None, 3001800933, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328188), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328189), 'NEAR', 2.27, None, 2788722437, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328189), 'JITOSOL', 184.28, None, 2708305830, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328190), 'ICP', 5.01, None, 2678580776, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328190), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328191), 'CRO', 0.082957, None, 2581099287, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328191), 'ONDO', 0.796336, None, 2515167110, 'coingecko')]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-03 19:24:28,340 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:28,341 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:24:28,342 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('coingecko',)
2025-07-03 19:24:28,345 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET error_count=$1::INTEGER, updated_at=now() WHERE data_sources.id = $2::UUID
2025-07-03 19:24:28,345 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] (4, UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:24:28,346 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:24:28,909 - app.data_sources.binance_client - INFO - Connected to Binance WebSocket
2025-07-03 19:24:28,910 - app.data_sources.binance_client - INFO - Subscribed to ticker data for 50 symbols
2025-07-03 19:24:50,608 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:50,609 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:24:50,609 - sqlalchemy.engine.Engine - INFO - [cached since 23.39s ago] ()
2025-07-03 19:24:50,610 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:25:27,228 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:25:27,229 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:25:27,230 - sqlalchemy.engine.Engine - INFO - [cached since 60.01s ago] ()
2025-07-03 19:25:27,231 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:25:27,232 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:25:31,710 - main - INFO - Shutting down Nexus application...
2025-07-03 19:25:42,591 - main - INFO - Starting Nexus application...
2025-07-03 19:25:42,613 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:25:42,613 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:42,614 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:25:42,614 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:42,615 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:25:42,615 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:42,616 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:25:42,616 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:25:42,616 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:25:42,617 - app.core.database - INFO - Database connection successful
2025-07-03 19:25:42,617 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:25:42,617 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:25:42,617 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:25:42,619 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,619 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,621 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,621 - sqlalchemy.engine.Engine - INFO - [cached since 0.0023s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,622 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,622 - sqlalchemy.engine.Engine - INFO - [cached since 0.002847s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,622 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,622 - sqlalchemy.engine.Engine - INFO - [cached since 0.003413s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,623 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,623 - sqlalchemy.engine.Engine - INFO - [cached since 0.004021s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,623 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,623 - sqlalchemy.engine.Engine - INFO - [cached since 0.004506s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,624 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,624 - sqlalchemy.engine.Engine - INFO - [cached since 0.005015s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,624 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:25:42,624 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:25:42,625 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:25:42,625 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:25:42,625 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:25:42,626 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:25:42,626 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:25:42,626 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:25:42,626 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:42,627 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:25:42,627 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:25:42,627 - main - INFO - Database initialized successfully
2025-07-03 19:25:46,668 - main - INFO - Shutting down Nexus application...
2025-07-03 19:25:47,151 - main - INFO - Starting Nexus application...
2025-07-03 19:25:47,174 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:25:47,174 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:47,175 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:25:47,175 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:47,176 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:25:47,176 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:47,177 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:25:47,177 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:25:47,177 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:25:47,178 - app.core.database - INFO - Database connection successful
2025-07-03 19:25:47,178 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:25:47,178 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:47,178 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:25:47,180 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,180 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,182 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,182 - sqlalchemy.engine.Engine - INFO - [cached since 0.002293s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,183 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,183 - sqlalchemy.engine.Engine - INFO - [cached since 0.002974s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,183 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,184 - sqlalchemy.engine.Engine - INFO - [cached since 0.003516s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,184 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,184 - sqlalchemy.engine.Engine - INFO - [cached since 0.004072s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,184 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,185 - sqlalchemy.engine.Engine - INFO - [cached since 0.004557s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,185 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,185 - sqlalchemy.engine.Engine - INFO - [cached since 0.005316s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,186 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:25:47,186 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:25:47,186 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:25:47,187 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:25:47,187 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:25:47,187 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:47,188 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:25:47,188 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:25:47,188 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:47,188 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:25:47,188 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:25:47,189 - main - INFO - Database initialized successfully
2025-07-03 19:25:58,745 - main - INFO - Shutting down Nexus application...
2025-07-03 19:25:59,576 - main - INFO - Starting Nexus application...
2025-07-03 19:25:59,599 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:25:59,599 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:59,600 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:25:59,600 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:59,601 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:25:59,601 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:59,602 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:25:59,602 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:25:59,602 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:25:59,603 - app.core.database - INFO - Database connection successful
2025-07-03 19:25:59,603 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:25:59,603 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:25:59,604 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:25:59,605 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,606 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,608 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,608 - sqlalchemy.engine.Engine - INFO - [cached since 0.002382s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,608 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,608 - sqlalchemy.engine.Engine - INFO - [cached since 0.002951s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,609 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,609 - sqlalchemy.engine.Engine - INFO - [cached since 0.003735s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,610 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,610 - sqlalchemy.engine.Engine - INFO - [cached since 0.004483s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,610 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,610 - sqlalchemy.engine.Engine - INFO - [cached since 0.005014s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,611 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,611 - sqlalchemy.engine.Engine - INFO - [cached since 0.005598s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,611 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:25:59,611 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:25:59,612 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:25:59,612 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:25:59,612 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:25:59,613 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:59,613 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:25:59,613 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:25:59,613 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:25:59,614 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:25:59,614 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:25:59,614 - main - INFO - Database initialized successfully
