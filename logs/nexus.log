2025-07-03 19:19:42,188 - main - INFO - Starting Nexus application...
2025-07-03 19:19:42,212 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:19:42,213 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:42,214 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:19:42,214 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:42,215 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:19:42,215 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:42,216 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:19:42,216 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:19:42,216 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:19:42,217 - app.core.database - INFO - Database connection successful
2025-07-03 19:19:42,217 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:19:42,217 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:19:42,217 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:19:42,219 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,219 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,221 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,221 - sqlalchemy.engine.Engine - INFO - [cached since 0.002443s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,222 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,222 - sqlalchemy.engine.Engine - INFO - [cached since 0.002994s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,222 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,222 - sqlalchemy.engine.Engine - INFO - [cached since 0.003455s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,223 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,223 - sqlalchemy.engine.Engine - INFO - [cached since 0.004258s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,224 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,224 - sqlalchemy.engine.Engine - INFO - [cached since 0.004964s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,225 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:42,225 - sqlalchemy.engine.Engine - INFO - [cached since 0.005652s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:42,226 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE social_mentions (
	time TIMESTAMP WITH TIME ZONE NOT NULL, 
	mention_id UUID NOT NULL, 
	symbol VARCHAR(20), 
	platform VARCHAR(20), 
	account VARCHAR(100), 
	content TEXT, 
	engagement_score NUMERIC(10, 2), 
	sentiment_score NUMERIC(3, 2), 
	source_url TEXT, 
	created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), 
	PRIMARY KEY (time, mention_id)
)


2025-07-03 19:19:42,226 - sqlalchemy.engine.Engine - INFO - [no key 0.00014s] ()
2025-07-03 19:19:42,231 - sqlalchemy.engine.Engine - INFO - CREATE INDEX idx_social_mentions_symbol_time ON social_mentions (symbol, time)
2025-07-03 19:19:42,231 - sqlalchemy.engine.Engine - INFO - [no key 0.00016s] ()
2025-07-03 19:19:42,233 - sqlalchemy.engine.Engine - INFO - CREATE INDEX idx_social_mentions_time_platform ON social_mentions (time, platform)
2025-07-03 19:19:42,233 - sqlalchemy.engine.Engine - INFO - [no key 0.00015s] ()
2025-07-03 19:19:42,235 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE data_sources (
	id UUID NOT NULL, 
	name VARCHAR(50) NOT NULL, 
	source_type VARCHAR(20) NOT NULL, 
	is_active BOOLEAN, 
	last_successful_fetch TIMESTAMP WITH TIME ZONE, 
	last_error TEXT, 
	error_count INTEGER, 
	requests_per_minute INTEGER, 
	current_usage INTEGER, 
	created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), 
	updated_at TIMESTAMP WITH TIME ZONE, 
	PRIMARY KEY (id), 
	UNIQUE (name)
)


2025-07-03 19:19:42,235 - sqlalchemy.engine.Engine - INFO - [no key 0.00011s] ()
2025-07-03 19:19:42,238 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:19:42,238 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:19:42,238 - sqlalchemy.engine.Engine - INFO - [generated in 0.00009s] ()
2025-07-03 19:19:42,241 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:19:42,241 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:19:42,241 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ()
2025-07-03 19:19:42,243 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:19:42,244 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:19:42,244 - sqlalchemy.engine.Engine - INFO - [generated in 0.00009s] ()
2025-07-03 19:19:42,245 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:19:42,245 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:19:42,246 - main - INFO - Database initialized successfully
2025-07-03 19:19:54,939 - main - INFO - Shutting down Nexus application...
2025-07-03 19:19:55,835 - main - INFO - Starting Nexus application...
2025-07-03 19:19:55,860 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:19:55,860 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:55,861 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:19:55,861 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:55,862 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:19:55,862 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:19:55,863 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:19:55,864 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:19:55,864 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:19:55,865 - app.core.database - INFO - Database connection successful
2025-07-03 19:19:55,865 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:19:55,865 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:19:55,866 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:19:55,868 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,868 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,870 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,871 - sqlalchemy.engine.Engine - INFO - [cached since 0.00285s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,871 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,871 - sqlalchemy.engine.Engine - INFO - [cached since 0.003749s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,872 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,872 - sqlalchemy.engine.Engine - INFO - [cached since 0.004448s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,873 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,873 - sqlalchemy.engine.Engine - INFO - [cached since 0.00525s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,874 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,874 - sqlalchemy.engine.Engine - INFO - [cached since 0.006043s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,874 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:19:55,874 - sqlalchemy.engine.Engine - INFO - [cached since 0.006651s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:19:55,875 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:19:55,875 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:19:55,875 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:19:55,876 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:19:55,876 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:19:55,877 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:19:55,877 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:19:55,877 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:19:55,877 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:19:55,878 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:19:55,878 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:19:55,879 - main - INFO - Database initialized successfully
2025-07-03 19:20:33,201 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:20:33,205 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:20:33,205 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:20:33,207 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:20:39,835 - app.services.data_collector - INFO - Starting data collection service
2025-07-03 19:20:41,366 - app.services.data_collector - INFO - Monitoring 50 symbols: ['BTC', 'ETH', 'USDC', 'SOL', 'FDUSD', 'XRP', 'PEPE', 'DOGE', 'SUI', 'NEIRO']...
2025-07-03 19:20:41,367 - app.services.data_collector - INFO - Starting Binance WebSocket data collection
2025-07-03 19:20:41,392 - app.services.data_collector - INFO - Starting CoinGecko data collection
2025-07-03 19:20:41,392 - app.data_sources.coingecko_client - ERROR - Error making request to coins/markets: Invalid variable type: value should be str, int or float, got False of type <class 'bool'>
2025-07-03 19:20:41,392 - app.services.data_collector - INFO - Starting health check loop
2025-07-03 19:20:41,393 - app.services.data_collector - WARNING - Binance WebSocket disconnected
2025-07-03 19:20:41,393 - app.services.data_collector - ERROR - Error in CoinGecko data collection: Invalid variable type: value should be str, int or float, got False of type <class 'bool'>
2025-07-03 19:20:41,395 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:20:41,395 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:20:41,395 - sqlalchemy.engine.Engine - INFO - [cached since 8.19s ago] ()
2025-07-03 19:20:41,396 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:20:41,396 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:20:41,418 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:20:41,420 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:20:41,420 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ('coingecko',)
2025-07-03 19:20:41,422 - sqlalchemy.engine.Engine - INFO - INSERT INTO data_sources (id, name, source_type, is_active, last_successful_fetch, last_error, error_count, requests_per_minute, current_usage, updated_at) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::BOOLEAN, $5::TIMESTAMP WITH TIME ZONE, $6::VARCHAR, $7::INTEGER, $8::INTEGER, $9::INTEGER, $10::TIMESTAMP WITH TIME ZONE) RETURNING data_sources.created_at
2025-07-03 19:20:41,422 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] (UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'), 'coingecko', 'api', True, None, "Invalid variable type: value should be str, int or float, got False of type <class 'bool'>", 1, None, 0, None)
2025-07-03 19:20:41,423 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:20:43,267 - app.data_sources.binance_client - INFO - Connected to Binance WebSocket
2025-07-03 19:20:43,269 - app.data_sources.binance_client - INFO - Subscribed to ticker data for 50 symbols
2025-07-03 19:20:51,757 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:20:51,757 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:20:51,758 - sqlalchemy.engine.Engine - INFO - [cached since 18.55s ago] ()
2025-07-03 19:20:51,759 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:21:17,964 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:21:17,965 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:21:17,966 - sqlalchemy.engine.Engine - INFO - [cached since 44.76s ago] ()
2025-07-03 19:21:17,969 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:21:41,401 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:21:41,401 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:21:41,402 - sqlalchemy.engine.Engine - INFO - [cached since 68.2s ago] ()
2025-07-03 19:21:41,403 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:21:41,404 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:21:41,424 - app.data_sources.coingecko_client - ERROR - Error making request to coins/markets: Invalid variable type: value should be str, int or float, got False of type <class 'bool'>
2025-07-03 19:21:41,424 - app.services.data_collector - ERROR - Error in CoinGecko data collection: Invalid variable type: value should be str, int or float, got False of type <class 'bool'>
2025-07-03 19:21:41,426 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:21:41,426 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:21:41,426 - sqlalchemy.engine.Engine - INFO - [cached since 60.01s ago] ('coingecko',)
2025-07-03 19:21:41,428 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET error_count=$1::INTEGER, updated_at=now() WHERE data_sources.id = $2::UUID
2025-07-03 19:21:41,428 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] (2, UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:21:41,429 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:05,632 - main - INFO - Shutting down Nexus application...
2025-07-03 19:22:16,506 - main - INFO - Starting Nexus application...
2025-07-03 19:22:16,529 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:22:16,529 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:16,530 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:22:16,530 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:16,531 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:22:16,531 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:16,532 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:22:16,533 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:22:16,533 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ()
2025-07-03 19:22:16,533 - app.core.database - INFO - Database connection successful
2025-07-03 19:22:16,534 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:22:16,534 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:22:16,534 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:22:16,536 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,536 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,538 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,538 - sqlalchemy.engine.Engine - INFO - [cached since 0.002315s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,539 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,539 - sqlalchemy.engine.Engine - INFO - [cached since 0.002916s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,539 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,539 - sqlalchemy.engine.Engine - INFO - [cached since 0.003619s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,540 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,540 - sqlalchemy.engine.Engine - INFO - [cached since 0.004263s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,540 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,541 - sqlalchemy.engine.Engine - INFO - [cached since 0.004764s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,541 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:16,541 - sqlalchemy.engine.Engine - INFO - [cached since 0.0053s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:16,541 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:22:16,542 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:22:16,542 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:16,542 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:22:16,543 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:22:16,543 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:22:16,543 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:22:16,543 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:22:16,544 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:22:16,544 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:22:16,544 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:16,544 - main - INFO - Database initialized successfully
2025-07-03 19:22:19,041 - main - INFO - Shutting down Nexus application...
2025-07-03 19:22:19,559 - main - INFO - Starting Nexus application...
2025-07-03 19:22:19,583 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:22:19,584 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:19,585 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:22:19,585 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:19,586 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:22:19,586 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:19,587 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:22:19,587 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:22:19,587 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:22:19,588 - app.core.database - INFO - Database connection successful
2025-07-03 19:22:19,588 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:22:19,588 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:22:19,588 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:22:19,590 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,590 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,592 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,592 - sqlalchemy.engine.Engine - INFO - [cached since 0.002419s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,593 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,593 - sqlalchemy.engine.Engine - INFO - [cached since 0.002972s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,593 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,594 - sqlalchemy.engine.Engine - INFO - [cached since 0.00349s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,594 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,594 - sqlalchemy.engine.Engine - INFO - [cached since 0.004119s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,595 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,595 - sqlalchemy.engine.Engine - INFO - [cached since 0.004662s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,595 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:19,595 - sqlalchemy.engine.Engine - INFO - [cached since 0.005318s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:19,596 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:22:19,596 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:22:19,596 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:22:19,597 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:22:19,597 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:22:19,597 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:19,598 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:22:19,598 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:22:19,598 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:19,598 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:22:19,598 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:19,599 - main - INFO - Database initialized successfully
2025-07-03 19:22:41,065 - main - INFO - Shutting down Nexus application...
2025-07-03 19:22:41,620 - main - INFO - Starting Nexus application...
2025-07-03 19:22:41,643 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:22:41,643 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:41,645 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:22:41,645 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:41,646 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:22:41,646 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:41,647 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:22:41,647 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:22:41,647 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:22:41,648 - app.core.database - INFO - Database connection successful
2025-07-03 19:22:41,648 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:22:41,648 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:22:41,649 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:22:41,651 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,651 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,653 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,653 - sqlalchemy.engine.Engine - INFO - [cached since 0.00278s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,654 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,654 - sqlalchemy.engine.Engine - INFO - [cached since 0.003678s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,655 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,655 - sqlalchemy.engine.Engine - INFO - [cached since 0.004312s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,655 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,656 - sqlalchemy.engine.Engine - INFO - [cached since 0.004946s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,656 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,656 - sqlalchemy.engine.Engine - INFO - [cached since 0.005441s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,656 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:41,657 - sqlalchemy.engine.Engine - INFO - [cached since 0.005941s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:41,657 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:22:41,657 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:22:41,657 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:22:41,658 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:22:41,658 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:22:41,658 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:41,659 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:22:41,659 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:22:41,659 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:22:41,659 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:22:41,660 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:41,660 - main - INFO - Database initialized successfully
2025-07-03 19:22:53,252 - main - INFO - Shutting down Nexus application...
2025-07-03 19:22:54,129 - main - INFO - Starting Nexus application...
2025-07-03 19:22:54,152 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:22:54,153 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:54,154 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:22:54,154 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:54,155 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:22:54,155 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:22:54,156 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:22:54,156 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:22:54,156 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:22:54,157 - app.core.database - INFO - Database connection successful
2025-07-03 19:22:54,157 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:22:54,157 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ()
2025-07-03 19:22:54,158 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:22:54,160 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,160 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,162 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,162 - sqlalchemy.engine.Engine - INFO - [cached since 0.002477s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,163 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,163 - sqlalchemy.engine.Engine - INFO - [cached since 0.003306s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,163 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,164 - sqlalchemy.engine.Engine - INFO - [cached since 0.003937s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,164 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,164 - sqlalchemy.engine.Engine - INFO - [cached since 0.004598s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,165 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,165 - sqlalchemy.engine.Engine - INFO - [cached since 0.005091s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,165 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:22:54,165 - sqlalchemy.engine.Engine - INFO - [cached since 0.00564s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:22:54,166 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:22:54,166 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:22:54,166 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:22:54,167 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:22:54,167 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:22:54,167 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:22:54,167 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:22:54,167 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:22:54,168 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:22:54,168 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:22:54,168 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:22:54,169 - main - INFO - Database initialized successfully
2025-07-03 19:23:02,049 - app.services.data_collector - INFO - Starting data collection service
2025-07-03 19:23:04,015 - app.services.data_collector - INFO - Monitoring 50 symbols: ['BTC', 'ETH', 'USDC', 'SOL', 'FDUSD', 'XRP', 'PEPE', 'DOGE', 'SUI', 'NEIRO']...
2025-07-03 19:23:04,016 - app.services.data_collector - INFO - Starting Binance WebSocket data collection
2025-07-03 19:23:04,036 - app.services.data_collector - INFO - Starting CoinGecko data collection
2025-07-03 19:23:04,037 - app.services.data_collector - INFO - Starting health check loop
2025-07-03 19:23:04,037 - app.services.data_collector - WARNING - Binance WebSocket disconnected
2025-07-03 19:23:04,039 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:23:04,043 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:23:04,044 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ()
2025-07-03 19:23:04,046 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:23:04,046 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:23:04,681 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:23:04,688 - sqlalchemy.engine.Engine - INFO - INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR) ON CONFLICT (time, symbol) DO UPDATE SET price = excluded.price, volume = excluded.volume, market_cap = excluded.market_cap, source = excluded.source
2025-07-03 19:23:04,689 - sqlalchemy.engine.Engine - INFO - [no key 0.00114s] (datetime.datetime(2025, 7, 3, 19, 23, 4, 674913), 'BTC', 109886, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674930), 'ETH', 2591.98, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674932), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674934), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674936), 'BNB', 661.31, None, 96468274517, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674938), 'SOL', 151.85, None, 81176547076, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674940), 'USDC', 0.999904, None, 62062221900, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674942), 'TRX', 0.285818, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674945), 'DOGE' ... 200 parameters truncated ... 3013202774, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675017), 'OKB', 50.03, None, 3003018801, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675019), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675020), 'NEAR', 2.27, None, 2788722437, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675022), 'JITOSOL', 184.23, None, 2708305830, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675023), 'ICP', 5.01, None, 2675746100, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675025), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675026), 'CRO', 0.082944, None, 2579830012, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675028), 'ONDO', 0.796419, None, 2515167110, 'coingecko')
2025-07-03 19:23:04,695 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:23:04,695 - app.services.data_storage - ERROR - Failed to store market data batch: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.InvalidColumnReferenceError'>: there is no unique or exclusion constraint matching the ON CONFLICT specification
[SQL: INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR) ON CONFLICT (time, symbol) DO UPDATE SET price = excluded.price, volume = excluded.volume, market_cap = excluded.market_cap, source = excluded.source]
[parameters: (datetime.datetime(2025, 7, 3, 19, 23, 4, 674913), 'BTC', 109886, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674930), 'ETH', 2591.98, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674932), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674934), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674936), 'BNB', 661.31, None, 96468274517, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674938), 'SOL', 151.85, None, 81176547076, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674940), 'USDC', 0.999904, None, 62062221900, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674942), 'TRX', 0.285818, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 674945), 'DOGE' ... 200 parameters truncated ... 3013202774, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675017), 'OKB', 50.03, None, 3003018801, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675019), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675020), 'NEAR', 2.27, None, 2788722437, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675022), 'JITOSOL', 184.23, None, 2708305830, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675023), 'ICP', 5.01, None, 2675746100, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675025), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675026), 'CRO', 0.082944, None, 2579830012, 'coingecko', datetime.datetime(2025, 7, 3, 19, 23, 4, 675028), 'ONDO', 0.796419, None, 2515167110, 'coingecko')]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-03 19:23:04,697 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:23:04,699 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:23:04,700 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] ('coingecko',)
2025-07-03 19:23:04,703 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET last_error=$1::VARCHAR, error_count=$2::INTEGER, updated_at=now() WHERE data_sources.id = $3::UUID
2025-07-03 19:23:04,704 - sqlalchemy.engine.Engine - INFO - [generated in 0.00033s] ('No data stored', 3, UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:23:04,705 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:23:05,705 - app.data_sources.binance_client - INFO - Connected to Binance WebSocket
2025-07-03 19:23:05,706 - app.data_sources.binance_client - INFO - Subscribed to ticker data for 50 symbols
2025-07-03 19:23:37,204 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:23:37,205 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:23:37,205 - sqlalchemy.engine.Engine - INFO - [cached since 33.16s ago] ()
2025-07-03 19:23:37,206 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:24:04,053 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:04,054 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:24:04,054 - sqlalchemy.engine.Engine - INFO - [cached since 60.01s ago] ()
2025-07-03 19:24:04,056 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:24:04,057 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:24:06,552 - main - INFO - Shutting down Nexus application...
2025-07-03 19:24:17,430 - main - INFO - Starting Nexus application...
2025-07-03 19:24:17,452 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:24:17,453 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:17,454 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:24:17,454 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:17,455 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:24:17,455 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:17,455 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:17,456 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:24:17,456 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:24:17,456 - app.core.database - INFO - Database connection successful
2025-07-03 19:24:17,457 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:24:17,457 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:17,457 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:24:17,459 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,459 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,461 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,461 - sqlalchemy.engine.Engine - INFO - [cached since 0.002615s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,462 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,462 - sqlalchemy.engine.Engine - INFO - [cached since 0.003241s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,463 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,463 - sqlalchemy.engine.Engine - INFO - [cached since 0.003859s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,463 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,463 - sqlalchemy.engine.Engine - INFO - [cached since 0.004399s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,464 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,464 - sqlalchemy.engine.Engine - INFO - [cached since 0.005161s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,465 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:17,465 - sqlalchemy.engine.Engine - INFO - [cached since 0.005799s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:17,465 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:24:17,465 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:24:17,465 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:17,466 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:24:17,466 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:24:17,466 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:17,467 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:24:17,467 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:24:17,467 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:17,468 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:24:17,468 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:24:17,468 - main - INFO - Database initialized successfully
2025-07-03 19:24:18,817 - main - INFO - Shutting down Nexus application...
2025-07-03 19:24:19,741 - main - INFO - Starting Nexus application...
2025-07-03 19:24:19,770 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:24:19,770 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:19,772 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:24:19,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:19,773 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:24:19,773 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:24:19,774 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:19,774 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:24:19,774 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:24:19,775 - app.core.database - INFO - Database connection successful
2025-07-03 19:24:19,775 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:24:19,775 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:24:19,776 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:24:19,777 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,778 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,780 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,780 - sqlalchemy.engine.Engine - INFO - [cached since 0.002805s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,781 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,781 - sqlalchemy.engine.Engine - INFO - [cached since 0.003573s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,781 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,782 - sqlalchemy.engine.Engine - INFO - [cached since 0.004225s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,782 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,782 - sqlalchemy.engine.Engine - INFO - [cached since 0.004816s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,783 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,783 - sqlalchemy.engine.Engine - INFO - [cached since 0.005347s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,783 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:24:19,783 - sqlalchemy.engine.Engine - INFO - [cached since 0.005867s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:24:19,784 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:24:19,784 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:24:19,784 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:24:19,785 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:24:19,785 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:24:19,785 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:24:19,785 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:24:19,786 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:24:19,786 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ()
2025-07-03 19:24:19,787 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:24:19,787 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:24:19,787 - main - INFO - Database initialized successfully
2025-07-03 19:24:26,073 - app.services.data_collector - INFO - Starting data collection service
2025-07-03 19:24:27,186 - app.services.data_collector - INFO - Monitoring 50 symbols: ['BTC', 'ETH', 'USDC', 'SOL', 'FDUSD', 'XRP', 'PEPE', 'DOGE', 'SUI', 'NEIRO']...
2025-07-03 19:24:27,187 - app.services.data_collector - INFO - Starting Binance WebSocket data collection
2025-07-03 19:24:27,211 - app.services.data_collector - INFO - Starting CoinGecko data collection
2025-07-03 19:24:27,211 - app.services.data_collector - INFO - Starting health check loop
2025-07-03 19:24:27,212 - app.services.data_collector - WARNING - Binance WebSocket disconnected
2025-07-03 19:24:27,214 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:27,218 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:24:27,218 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ()
2025-07-03 19:24:27,221 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:24:27,221 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:24:28,330 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:28,334 - sqlalchemy.engine.Engine - INFO - INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR) ON CONFLICT (time, symbol) DO UPDATE SET price = excluded.price, volume = excluded.volume, market_cap = excluded.market_cap, source = excluded.source
2025-07-03 19:24:28,335 - sqlalchemy.engine.Engine - INFO - [no key 0.00090s] (datetime.datetime(2025, 7, 3, 19, 24, 28, 328144), 'BTC', 109889, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328153), 'ETH', 2591.36, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328154), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328158), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328159), 'BNB', 661.65, None, 96468274517, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328160), 'SOL', 151.87, None, 81176547076, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328160), 'USDC', 0.99991, None, 62062004477, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328161), 'TRX', 0.285814, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328162), 'DOGE' ... 200 parameters truncated ... 3013202774, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328188), 'OKB', 50.03, None, 3001800933, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328188), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328189), 'NEAR', 2.27, None, 2788722437, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328189), 'JITOSOL', 184.28, None, 2708305830, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328190), 'ICP', 5.01, None, 2678580776, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328190), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328191), 'CRO', 0.082957, None, 2581099287, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328191), 'ONDO', 0.796336, None, 2515167110, 'coingecko')
2025-07-03 19:24:28,338 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:24:28,339 - app.services.data_storage - ERROR - Failed to store market data batch: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.InvalidColumnReferenceError'>: there is no unique or exclusion constraint matching the ON CONFLICT specification
[SQL: INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR) ON CONFLICT (time, symbol) DO UPDATE SET price = excluded.price, volume = excluded.volume, market_cap = excluded.market_cap, source = excluded.source]
[parameters: (datetime.datetime(2025, 7, 3, 19, 24, 28, 328144), 'BTC', 109889, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328153), 'ETH', 2591.36, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328154), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328158), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328159), 'BNB', 661.65, None, 96468274517, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328160), 'SOL', 151.87, None, 81176547076, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328160), 'USDC', 0.99991, None, 62062004477, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328161), 'TRX', 0.285814, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328162), 'DOGE' ... 200 parameters truncated ... 3013202774, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328188), 'OKB', 50.03, None, 3001800933, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328188), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328189), 'NEAR', 2.27, None, 2788722437, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328189), 'JITOSOL', 184.28, None, 2708305830, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328190), 'ICP', 5.01, None, 2678580776, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328190), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328191), 'CRO', 0.082957, None, 2581099287, 'coingecko', datetime.datetime(2025, 7, 3, 19, 24, 28, 328191), 'ONDO', 0.796336, None, 2515167110, 'coingecko')]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-03 19:24:28,340 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:28,341 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:24:28,342 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('coingecko',)
2025-07-03 19:24:28,345 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET error_count=$1::INTEGER, updated_at=now() WHERE data_sources.id = $2::UUID
2025-07-03 19:24:28,345 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] (4, UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:24:28,346 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:24:28,909 - app.data_sources.binance_client - INFO - Connected to Binance WebSocket
2025-07-03 19:24:28,910 - app.data_sources.binance_client - INFO - Subscribed to ticker data for 50 symbols
2025-07-03 19:24:50,608 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:24:50,609 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:24:50,609 - sqlalchemy.engine.Engine - INFO - [cached since 23.39s ago] ()
2025-07-03 19:24:50,610 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:25:27,228 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:25:27,229 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:25:27,230 - sqlalchemy.engine.Engine - INFO - [cached since 60.01s ago] ()
2025-07-03 19:25:27,231 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:25:27,232 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:25:31,710 - main - INFO - Shutting down Nexus application...
2025-07-03 19:25:42,591 - main - INFO - Starting Nexus application...
2025-07-03 19:25:42,613 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:25:42,613 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:42,614 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:25:42,614 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:42,615 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:25:42,615 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:42,616 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:25:42,616 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:25:42,616 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:25:42,617 - app.core.database - INFO - Database connection successful
2025-07-03 19:25:42,617 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:25:42,617 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:25:42,617 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:25:42,619 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,619 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,621 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,621 - sqlalchemy.engine.Engine - INFO - [cached since 0.0023s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,622 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,622 - sqlalchemy.engine.Engine - INFO - [cached since 0.002847s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,622 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,622 - sqlalchemy.engine.Engine - INFO - [cached since 0.003413s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,623 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,623 - sqlalchemy.engine.Engine - INFO - [cached since 0.004021s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,623 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,623 - sqlalchemy.engine.Engine - INFO - [cached since 0.004506s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,624 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:42,624 - sqlalchemy.engine.Engine - INFO - [cached since 0.005015s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:42,624 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:25:42,624 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:25:42,625 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:25:42,625 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:25:42,625 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:25:42,626 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:25:42,626 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:25:42,626 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:25:42,626 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:42,627 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:25:42,627 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:25:42,627 - main - INFO - Database initialized successfully
2025-07-03 19:25:46,668 - main - INFO - Shutting down Nexus application...
2025-07-03 19:25:47,151 - main - INFO - Starting Nexus application...
2025-07-03 19:25:47,174 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:25:47,174 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:47,175 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:25:47,175 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:47,176 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:25:47,176 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:47,177 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:25:47,177 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:25:47,177 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:25:47,178 - app.core.database - INFO - Database connection successful
2025-07-03 19:25:47,178 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:25:47,178 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:47,178 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:25:47,180 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,180 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,182 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,182 - sqlalchemy.engine.Engine - INFO - [cached since 0.002293s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,183 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,183 - sqlalchemy.engine.Engine - INFO - [cached since 0.002974s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,183 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,184 - sqlalchemy.engine.Engine - INFO - [cached since 0.003516s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,184 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,184 - sqlalchemy.engine.Engine - INFO - [cached since 0.004072s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,184 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,185 - sqlalchemy.engine.Engine - INFO - [cached since 0.004557s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,185 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:47,185 - sqlalchemy.engine.Engine - INFO - [cached since 0.005316s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:47,186 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:25:47,186 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:25:47,186 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:25:47,187 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:25:47,187 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:25:47,187 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:47,188 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:25:47,188 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:25:47,188 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:47,188 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:25:47,188 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:25:47,189 - main - INFO - Database initialized successfully
2025-07-03 19:25:58,745 - main - INFO - Shutting down Nexus application...
2025-07-03 19:25:59,576 - main - INFO - Starting Nexus application...
2025-07-03 19:25:59,599 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:25:59,599 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:59,600 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:25:59,600 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:59,601 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:25:59,601 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:25:59,602 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:25:59,602 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:25:59,602 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:25:59,603 - app.core.database - INFO - Database connection successful
2025-07-03 19:25:59,603 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:25:59,603 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:25:59,604 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:25:59,605 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,606 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,608 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,608 - sqlalchemy.engine.Engine - INFO - [cached since 0.002382s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,608 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,608 - sqlalchemy.engine.Engine - INFO - [cached since 0.002951s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,609 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,609 - sqlalchemy.engine.Engine - INFO - [cached since 0.003735s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,610 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,610 - sqlalchemy.engine.Engine - INFO - [cached since 0.004483s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,610 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,610 - sqlalchemy.engine.Engine - INFO - [cached since 0.005014s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,611 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:25:59,611 - sqlalchemy.engine.Engine - INFO - [cached since 0.005598s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:25:59,611 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:25:59,611 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:25:59,612 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:25:59,612 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:25:59,612 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:25:59,613 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:25:59,613 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:25:59,613 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:25:59,613 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:25:59,614 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:25:59,614 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:25:59,614 - main - INFO - Database initialized successfully
2025-07-03 19:26:05,987 - app.services.data_collector - INFO - Starting data collection service
2025-07-03 19:26:07,120 - app.services.data_collector - INFO - Monitoring 50 symbols: ['BTC', 'ETH', 'USDC', 'SOL', 'FDUSD', 'XRP', 'PEPE', 'DOGE', 'SUI', 'NEIRO']...
2025-07-03 19:26:07,121 - app.services.data_collector - INFO - Starting Binance WebSocket data collection
2025-07-03 19:26:07,145 - app.services.data_collector - INFO - Starting CoinGecko data collection
2025-07-03 19:26:07,145 - app.services.data_collector - INFO - Starting health check loop
2025-07-03 19:26:07,145 - app.services.data_collector - WARNING - Binance WebSocket disconnected
2025-07-03 19:26:07,147 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:26:07,152 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:26:07,152 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ()
2025-07-03 19:26:07,154 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:26:07,154 - app.services.data_collector - INFO - Currently tracking 0 symbols with data
2025-07-03 19:26:07,668 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:26:07,675 - sqlalchemy.engine.Engine - INFO - INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR)
2025-07-03 19:26:07,676 - sqlalchemy.engine.Engine - INFO - [no key 0.00122s] (datetime.datetime(2025, 7, 3, 19, 26, 7, 664256), 'BTC', 109895, None, 2185291291917, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664271), 'ETH', 2591.86, None, 312895955018, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664273), 'USDT', 1.0, None, 158327749053, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664274), 'XRP', 2.28, None, 134649094000, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664276), 'BNB', 661.67, None, 96529135094, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664277), 'SOL', 151.9, None, 81216887154, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664279), 'USDC', 0.999905, None, 62062004477, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664280), 'TRX', 0.285799, None, 27091750770, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664281), 'DOGE' ... 200 parameters truncated ... 3018450429, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664338), 'OKB', 50.03, None, 3001800933, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664340), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664341), 'NEAR', 2.27, None, 2793648414, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664342), 'JITOSOL', 184.32, None, 2710058028, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664343), 'ICP', 5.01, None, 2678580776, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664345), 'ETC', 17.04, None, 2600047888, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664346), 'CRO', 0.082954, None, 2581099287, 'coingecko', datetime.datetime(2025, 7, 3, 19, 26, 7, 664347), 'ONDO', 0.796455, None, 2516129558, 'coingecko')
2025-07-03 19:26:07,692 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:26:07,694 - app.services.data_storage - INFO - Stored batch of 50 market data points
2025-07-03 19:26:07,696 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:26:07,697 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:26:07,697 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] ('coingecko',)
2025-07-03 19:26:07,700 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET last_successful_fetch=$1::TIMESTAMP WITH TIME ZONE, last_error=$2::VARCHAR, error_count=$3::INTEGER, updated_at=now() WHERE data_sources.id = $4::UUID
2025-07-03 19:26:07,700 - sqlalchemy.engine.Engine - INFO - [generated in 0.00034s] (datetime.datetime(2025, 7, 3, 19, 26, 7, 699367), None, 0, UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:26:07,702 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:26:07,703 - app.services.data_collector - INFO - Stored 50 CoinGecko data points
2025-07-03 19:26:08,693 - app.data_sources.binance_client - INFO - Connected to Binance WebSocket
2025-07-03 19:26:08,696 - app.data_sources.binance_client - INFO - Subscribed to ticker data for 50 symbols
2025-07-03 19:26:33,913 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:26:33,914 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:26:33,915 - sqlalchemy.engine.Engine - INFO - [cached since 26.76s ago] ()
2025-07-03 19:26:33,916 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:26:42,412 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:26:42,414 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:26:42,414 - sqlalchemy.engine.Engine - INFO - [generated in 0.00034s] ('BTC', datetime.datetime(2025, 7, 3, 18, 26, 42, 410474))
2025-07-03 19:26:42,416 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:27:07,156 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:27:07,157 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:27:07,158 - sqlalchemy.engine.Engine - INFO - [cached since 60.01s ago] ()
2025-07-03 19:27:07,159 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:27:07,160 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:27:10,978 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:27:10,978 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:27:10,978 - sqlalchemy.engine.Engine - INFO - [cached since 63.83s ago] ()
2025-07-03 19:27:10,979 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:27:18,938 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:27:18,938 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:27:18,938 - sqlalchemy.engine.Engine - INFO - [cached since 36.52s ago] ('ETH', datetime.datetime(2025, 7, 3, 18, 27, 18, 937076))
2025-07-03 19:27:18,940 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:28:07,168 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:28:07,169 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:28:07,170 - sqlalchemy.engine.Engine - INFO - [cached since 120s ago] ()
2025-07-03 19:28:07,171 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:28:07,171 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:29:07,214 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:29:07,215 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:29:07,215 - sqlalchemy.engine.Engine - INFO - [cached since 180s ago] ()
2025-07-03 19:29:07,217 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:29:07,219 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:30:07,226 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:30:07,227 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:30:07,228 - sqlalchemy.engine.Engine - INFO - [cached since 240s ago] ()
2025-07-03 19:30:07,229 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:30:07,230 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:31:07,241 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:31:07,242 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:31:07,242 - sqlalchemy.engine.Engine - INFO - [cached since 300s ago] ()
2025-07-03 19:31:07,244 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:31:07,245 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:31:08,611 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:31:08,617 - sqlalchemy.engine.Engine - INFO - INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR)
2025-07-03 19:31:08,618 - sqlalchemy.engine.Engine - INFO - [no key 0.00140s] (datetime.datetime(2025, 7, 3, 19, 31, 8, 606750), 'BTC', 109903, None, 2185559024150, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606763), 'ETH', 2590.15, None, 312747627169, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606765), 'USDT', 1.0, None, 158324516417, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606767), 'XRP', 2.28, None, 134669391212, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606768), 'BNB', 661.84, None, 96553801054, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606770), 'SOL', 151.88, None, 81214741955, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606772), 'USDC', 0.999895, None, 62059931653, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606774), 'TRX', 0.285751, None, 27088089067, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606775), 'DOGE' ... 200 parameters truncated ... 3020666049, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606820), 'OKB', 50.02, None, 3001433832, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606821), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606822), 'NEAR', 2.27, None, 2798601969, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606823), 'JITOSOL', 184.46, None, 2712075870, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606825), 'ICP', 5.01, None, 2682257815, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606826), 'ETC', 17.04, None, 2599175653, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606827), 'CRO', 0.082967, None, 2581497827, 'coingecko', datetime.datetime(2025, 7, 3, 19, 31, 8, 606828), 'ONDO', 0.796724, None, 2516937620, 'coingecko')
2025-07-03 19:31:08,622 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:31:08,624 - app.services.data_storage - INFO - Stored batch of 50 market data points
2025-07-03 19:31:08,626 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:31:08,626 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:31:08,626 - sqlalchemy.engine.Engine - INFO - [cached since 300.9s ago] ('coingecko',)
2025-07-03 19:31:08,628 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET last_successful_fetch=$1::TIMESTAMP WITH TIME ZONE, updated_at=now() WHERE data_sources.id = $2::UUID
2025-07-03 19:31:08,629 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] (datetime.datetime(2025, 7, 3, 19, 31, 8, 627987), UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:31:08,630 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:31:08,631 - app.services.data_collector - INFO - Stored 50 CoinGecko data points
2025-07-03 19:32:07,248 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:32:07,249 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:32:07,249 - sqlalchemy.engine.Engine - INFO - [cached since 360.1s ago] ()
2025-07-03 19:32:07,251 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:32:07,252 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:32:39,945 - main - INFO - Shutting down Nexus application...
2025-07-03 19:32:50,832 - main - INFO - Starting Nexus application...
2025-07-03 19:32:50,855 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:32:50,855 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:32:50,856 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:32:50,856 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:32:50,857 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:32:50,857 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:32:50,858 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:32:50,858 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:32:50,858 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:32:50,859 - app.core.database - INFO - Database connection successful
2025-07-03 19:32:50,859 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:32:50,859 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:32:50,860 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:32:50,862 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:32:50,862 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:32:50,864 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:32:50,864 - sqlalchemy.engine.Engine - INFO - [cached since 0.002324s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:32:50,864 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:32:50,864 - sqlalchemy.engine.Engine - INFO - [cached since 0.002905s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:32:50,865 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:32:50,865 - sqlalchemy.engine.Engine - INFO - [cached since 0.003738s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:32:50,866 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:32:50,866 - sqlalchemy.engine.Engine - INFO - [cached since 0.004448s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:32:50,867 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:32:50,867 - sqlalchemy.engine.Engine - INFO - [cached since 0.005286s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:32:50,867 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:32:50,867 - sqlalchemy.engine.Engine - INFO - [cached since 0.00585s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:32:50,868 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:32:50,868 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:32:50,868 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:32:50,869 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:32:50,869 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:32:50,869 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:32:50,869 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:32:50,870 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:32:50,870 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:32:50,870 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:32:50,870 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:32:50,871 - main - INFO - Database initialized successfully
2025-07-03 19:33:03,010 - main - INFO - Shutting down Nexus application...
2025-07-03 19:33:03,600 - main - INFO - Starting Nexus application...
2025-07-03 19:33:03,623 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:33:03,624 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:33:03,625 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:33:03,625 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:33:03,626 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:33:03,626 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:33:03,627 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:33:03,627 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:33:03,627 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:33:03,628 - app.core.database - INFO - Database connection successful
2025-07-03 19:33:03,628 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:33:03,628 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ()
2025-07-03 19:33:03,629 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:33:03,630 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:03,631 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:03,633 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:03,633 - sqlalchemy.engine.Engine - INFO - [cached since 0.002401s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:03,633 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:03,633 - sqlalchemy.engine.Engine - INFO - [cached since 0.002935s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:03,634 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:03,634 - sqlalchemy.engine.Engine - INFO - [cached since 0.003515s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:03,634 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:03,635 - sqlalchemy.engine.Engine - INFO - [cached since 0.004138s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:03,635 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:03,635 - sqlalchemy.engine.Engine - INFO - [cached since 0.004731s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:03,636 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:03,636 - sqlalchemy.engine.Engine - INFO - [cached since 0.005328s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:03,636 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:33:03,636 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:33:03,637 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ()
2025-07-03 19:33:03,637 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:33:03,638 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:33:03,638 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:33:03,638 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:33:03,638 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:33:03,638 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:33:03,639 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:33:03,639 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:33:03,639 - main - INFO - Database initialized successfully
2025-07-03 19:33:26,291 - main - INFO - Shutting down Nexus application...
2025-07-03 19:33:26,840 - main - INFO - Starting Nexus application...
2025-07-03 19:33:26,864 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:33:26,864 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:33:26,865 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:33:26,865 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:33:26,866 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:33:26,866 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:33:26,867 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:33:26,867 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:33:26,867 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:33:26,868 - app.core.database - INFO - Database connection successful
2025-07-03 19:33:26,868 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:33:26,868 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:33:26,869 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:33:26,871 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:26,871 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:26,873 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:26,874 - sqlalchemy.engine.Engine - INFO - [cached since 0.002782s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:26,874 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:26,874 - sqlalchemy.engine.Engine - INFO - [cached since 0.003426s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:26,875 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:26,875 - sqlalchemy.engine.Engine - INFO - [cached since 0.003976s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:26,875 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:26,875 - sqlalchemy.engine.Engine - INFO - [cached since 0.004468s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:26,876 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:26,876 - sqlalchemy.engine.Engine - INFO - [cached since 0.005049s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:26,876 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:33:26,876 - sqlalchemy.engine.Engine - INFO - [cached since 0.005612s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:33:26,877 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:33:26,877 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:33:26,877 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:33:26,878 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:33:26,879 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:33:26,879 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:33:26,879 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:33:26,879 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:33:26,879 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ()
2025-07-03 19:33:26,880 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:33:26,880 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:33:26,880 - main - INFO - Database initialized successfully
2025-07-03 19:34:21,909 - main - INFO - Shutting down Nexus application...
2025-07-03 19:34:22,457 - main - INFO - Starting Nexus application...
2025-07-03 19:34:22,480 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:34:22,481 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:22,482 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:34:22,482 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:22,483 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:34:22,483 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:22,484 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:34:22,484 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:34:22,484 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ()
2025-07-03 19:34:22,485 - app.core.database - INFO - Database connection successful
2025-07-03 19:34:22,485 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:34:22,486 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:34:22,486 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:34:22,488 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:22,488 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:22,490 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:22,490 - sqlalchemy.engine.Engine - INFO - [cached since 0.002545s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:22,491 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:22,491 - sqlalchemy.engine.Engine - INFO - [cached since 0.003563s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:22,492 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:22,492 - sqlalchemy.engine.Engine - INFO - [cached since 0.004251s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:22,493 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:22,493 - sqlalchemy.engine.Engine - INFO - [cached since 0.005094s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:22,493 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:22,493 - sqlalchemy.engine.Engine - INFO - [cached since 0.005774s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:22,494 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:22,494 - sqlalchemy.engine.Engine - INFO - [cached since 0.006653s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:22,495 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:34:22,495 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:34:22,495 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:34:22,496 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:34:22,496 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:34:22,496 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:34:22,497 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:34:22,497 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:34:22,497 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:34:22,498 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:34:22,498 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:34:22,498 - main - INFO - Database initialized successfully
2025-07-03 19:34:36,613 - main - INFO - Shutting down Nexus application...
2025-07-03 19:34:37,202 - main - INFO - Starting Nexus application...
2025-07-03 19:34:37,225 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:34:37,225 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:37,226 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:34:37,226 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:37,227 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:34:37,227 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:37,228 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:34:37,229 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:34:37,229 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] ()
2025-07-03 19:34:37,229 - app.core.database - INFO - Database connection successful
2025-07-03 19:34:37,230 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:34:37,230 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ()
2025-07-03 19:34:37,230 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:34:37,232 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:37,232 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:37,234 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:37,234 - sqlalchemy.engine.Engine - INFO - [cached since 0.002243s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:37,235 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:37,235 - sqlalchemy.engine.Engine - INFO - [cached since 0.002829s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:37,235 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:37,235 - sqlalchemy.engine.Engine - INFO - [cached since 0.003424s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:37,236 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:37,236 - sqlalchemy.engine.Engine - INFO - [cached since 0.004091s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:37,236 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:37,237 - sqlalchemy.engine.Engine - INFO - [cached since 0.004768s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:37,237 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:37,237 - sqlalchemy.engine.Engine - INFO - [cached since 0.005507s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:37,238 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:34:37,238 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:34:37,238 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:34:37,239 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:34:37,239 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:34:37,239 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:34:37,240 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:34:37,240 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:34:37,240 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:34:37,240 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:34:37,241 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:34:37,241 - main - INFO - Database initialized successfully
2025-07-03 19:34:54,480 - main - INFO - Shutting down Nexus application...
2025-07-03 19:34:55,039 - main - INFO - Starting Nexus application...
2025-07-03 19:34:55,063 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:34:55,064 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:55,065 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:34:55,065 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:55,066 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:34:55,066 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:34:55,067 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:34:55,067 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:34:55,068 - sqlalchemy.engine.Engine - INFO - [generated in 0.00033s] ()
2025-07-03 19:34:55,068 - app.core.database - INFO - Database connection successful
2025-07-03 19:34:55,068 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:34:55,069 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:34:55,069 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:34:55,071 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:55,071 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:55,073 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:55,073 - sqlalchemy.engine.Engine - INFO - [cached since 0.002629s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:55,074 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:55,074 - sqlalchemy.engine.Engine - INFO - [cached since 0.003265s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:55,074 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:55,075 - sqlalchemy.engine.Engine - INFO - [cached since 0.003782s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:55,075 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:55,075 - sqlalchemy.engine.Engine - INFO - [cached since 0.004283s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:55,075 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:55,076 - sqlalchemy.engine.Engine - INFO - [cached since 0.004781s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:55,076 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:34:55,076 - sqlalchemy.engine.Engine - INFO - [cached since 0.005419s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:34:55,076 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:34:55,077 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:34:55,077 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:34:55,078 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:34:55,078 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:34:55,078 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ()
2025-07-03 19:34:55,079 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:34:55,079 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:34:55,079 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:34:55,080 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:34:55,080 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:34:55,080 - main - INFO - Database initialized successfully
2025-07-03 19:35:14,330 - main - INFO - Shutting down Nexus application...
2025-07-03 19:35:14,888 - main - INFO - Starting Nexus application...
2025-07-03 19:35:14,911 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:35:14,911 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:35:14,913 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:35:14,913 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:35:14,914 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:35:14,914 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:35:14,915 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:35:14,915 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:35:14,915 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ()
2025-07-03 19:35:14,916 - app.core.database - INFO - Database connection successful
2025-07-03 19:35:14,916 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:35:14,916 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:35:14,917 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:35:14,918 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:14,919 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:14,921 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:14,921 - sqlalchemy.engine.Engine - INFO - [cached since 0.00265s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:14,922 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:14,922 - sqlalchemy.engine.Engine - INFO - [cached since 0.00339s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:14,922 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:14,923 - sqlalchemy.engine.Engine - INFO - [cached since 0.004209s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:14,923 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:14,923 - sqlalchemy.engine.Engine - INFO - [cached since 0.005011s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:14,924 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:14,924 - sqlalchemy.engine.Engine - INFO - [cached since 0.005556s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:14,924 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:14,924 - sqlalchemy.engine.Engine - INFO - [cached since 0.006107s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:14,925 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:35:14,925 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:35:14,925 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:35:14,926 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:35:14,926 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:35:14,926 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:35:14,927 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:35:14,927 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:35:14,927 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:35:14,927 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:35:14,927 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:35:14,928 - main - INFO - Database initialized successfully
2025-07-03 19:35:55,629 - main - INFO - Shutting down Nexus application...
2025-07-03 19:35:56,180 - main - INFO - Starting Nexus application...
2025-07-03 19:35:56,205 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:35:56,206 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:35:56,207 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:35:56,207 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:35:56,208 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:35:56,209 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:35:56,210 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:35:56,210 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:35:56,210 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ()
2025-07-03 19:35:56,211 - app.core.database - INFO - Database connection successful
2025-07-03 19:35:56,211 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:35:56,211 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:35:56,212 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:35:56,214 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:56,214 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:56,217 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:56,217 - sqlalchemy.engine.Engine - INFO - [cached since 0.00365s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:56,218 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:56,218 - sqlalchemy.engine.Engine - INFO - [cached since 0.00464s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:56,219 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:56,219 - sqlalchemy.engine.Engine - INFO - [cached since 0.005303s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:56,219 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:56,220 - sqlalchemy.engine.Engine - INFO - [cached since 0.006196s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:56,220 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:56,220 - sqlalchemy.engine.Engine - INFO - [cached since 0.006913s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:56,221 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:35:56,221 - sqlalchemy.engine.Engine - INFO - [cached since 0.00765s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:35:56,222 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:35:56,222 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:35:56,222 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:35:56,224 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:35:56,224 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:35:56,224 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:35:56,225 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:35:56,225 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:35:56,225 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:35:56,226 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:35:56,226 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:35:56,226 - main - INFO - Database initialized successfully
2025-07-03 19:36:09,322 - main - INFO - Shutting down Nexus application...
2025-07-03 19:36:10,306 - main - INFO - Starting Nexus application...
2025-07-03 19:36:10,329 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:36:10,329 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:36:10,330 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:36:10,331 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:36:10,331 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:36:10,331 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:36:10,332 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:10,333 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:36:10,333 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] ()
2025-07-03 19:36:10,334 - app.core.database - INFO - Database connection successful
2025-07-03 19:36:10,334 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:36:10,334 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:36:10,334 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:36:10,336 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:36:10,336 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:36:10,339 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:36:10,339 - sqlalchemy.engine.Engine - INFO - [cached since 0.002574s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:36:10,339 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:36:10,339 - sqlalchemy.engine.Engine - INFO - [cached since 0.003267s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:36:10,340 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:36:10,340 - sqlalchemy.engine.Engine - INFO - [cached since 0.004086s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:36:10,341 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:36:10,341 - sqlalchemy.engine.Engine - INFO - [cached since 0.004847s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:36:10,341 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:36:10,342 - sqlalchemy.engine.Engine - INFO - [cached since 0.005387s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:36:10,342 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:36:10,342 - sqlalchemy.engine.Engine - INFO - [cached since 0.005927s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:36:10,342 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:36:10,343 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:36:10,343 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:36:10,343 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:36:10,344 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:36:10,344 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:36:10,344 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:36:10,344 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:36:10,345 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:36:10,345 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:36:10,345 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:36:10,345 - main - INFO - Database initialized successfully
2025-07-03 19:36:35,967 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:35,972 - sqlalchemy.engine.Engine - INFO - SELECT correlations.id, correlations.symbol, correlations.pattern_type, correlations.confidence_score, correlations.trigger_time, correlations.detection_time, correlations.evidence, correlations.status, correlations.price_change_1h, correlations.price_change_24h, correlations.created_at, correlations.updated_at 
FROM correlations 
WHERE correlations.trigger_time >= $1::TIMESTAMP WITH TIME ZONE AND correlations.status = $2::VARCHAR ORDER BY correlations.trigger_time DESC
2025-07-03 19:36:35,972 - sqlalchemy.engine.Engine - INFO - [generated in 0.00026s] (datetime.datetime(2025, 7, 2, 19, 36, 35, 964719), 'active')
2025-07-03 19:36:35,973 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:35,973 - app.services.pattern_service - ERROR - Error getting recent patterns: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column correlations.detection_time does not exist
[SQL: SELECT correlations.id, correlations.symbol, correlations.pattern_type, correlations.confidence_score, correlations.trigger_time, correlations.detection_time, correlations.evidence, correlations.status, correlations.price_change_1h, correlations.price_change_24h, correlations.created_at, correlations.updated_at 
FROM correlations 
WHERE correlations.trigger_time >= $1::TIMESTAMP WITH TIME ZONE AND correlations.status = $2::VARCHAR ORDER BY correlations.trigger_time DESC]
[parameters: (datetime.datetime(2025, 7, 2, 19, 36, 35, 964719), 'active')]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-03 19:36:35,974 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:35,975 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:36:35,975 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:36:35,977 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,951 - app.services.pattern_service - INFO - Starting pattern detection service
2025-07-03 19:36:47,953 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,953 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:36:47,953 - sqlalchemy.engine.Engine - INFO - [cached since 11.98s ago] ()
2025-07-03 19:36:47,954 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,955 - app.services.pattern_service - INFO - Running pattern detection for 50 symbols
2025-07-03 19:36:47,956 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,957 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:47,957 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] ('WETH', datetime.datetime(2025, 7, 3, 18, 36, 47, 955497))
2025-07-03 19:36:47,960 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,961 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,962 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:47,962 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('WETH', datetime.datetime(2025, 7, 3, 18, 36, 47, 961163))
2025-07-03 19:36:47,966 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,967 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,968 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:47,968 - sqlalchemy.engine.Engine - INFO - [cached since 0.01117s ago] ('APT', datetime.datetime(2025, 7, 3, 18, 36, 47, 966769))
2025-07-03 19:36:47,969 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,971 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,971 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:47,971 - sqlalchemy.engine.Engine - INFO - [cached since 0.008924s ago] ('APT', datetime.datetime(2025, 7, 3, 18, 36, 47, 970365))
2025-07-03 19:36:47,972 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,974 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,974 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:47,974 - sqlalchemy.engine.Engine - INFO - [cached since 0.01736s ago] ('USDE', datetime.datetime(2025, 7, 3, 18, 36, 47, 973309))
2025-07-03 19:36:47,975 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,977 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,977 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:47,977 - sqlalchemy.engine.Engine - INFO - [cached since 0.01482s ago] ('USDE', datetime.datetime(2025, 7, 3, 18, 36, 47, 976307))
2025-07-03 19:36:47,978 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,980 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,980 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:47,980 - sqlalchemy.engine.Engine - INFO - [cached since 0.02329s ago] ('CRO', datetime.datetime(2025, 7, 3, 18, 36, 47, 979504))
2025-07-03 19:36:47,981 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,984 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,984 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:47,984 - sqlalchemy.engine.Engine - INFO - [cached since 0.02206s ago] ('CRO', datetime.datetime(2025, 7, 3, 18, 36, 47, 983414))
2025-07-03 19:36:47,985 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,986 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,987 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:47,987 - sqlalchemy.engine.Engine - INFO - [cached since 0.03s ago] ('CBBTC', datetime.datetime(2025, 7, 3, 18, 36, 47, 986205))
2025-07-03 19:36:47,988 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,989 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,990 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:47,990 - sqlalchemy.engine.Engine - INFO - [cached since 0.02767s ago] ('CBBTC', datetime.datetime(2025, 7, 3, 18, 36, 47, 988851))
2025-07-03 19:36:47,991 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,992 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,993 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:47,993 - sqlalchemy.engine.Engine - INFO - [cached since 0.03591s ago] ('BTC', datetime.datetime(2025, 7, 3, 18, 36, 47, 991891))
2025-07-03 19:36:47,994 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,995 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,996 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:47,996 - sqlalchemy.engine.Engine - INFO - [cached since 0.03383s ago] ('BTC', datetime.datetime(2025, 7, 3, 18, 36, 47, 995166))
2025-07-03 19:36:47,997 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:47,999 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:47,999 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,000 - sqlalchemy.engine.Engine - INFO - [cached since 0.04273s ago] ('AAVE', datetime.datetime(2025, 7, 3, 18, 36, 47, 998560))
2025-07-03 19:36:48,001 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,003 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,003 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,003 - sqlalchemy.engine.Engine - INFO - [cached since 0.04095s ago] ('AAVE', datetime.datetime(2025, 7, 3, 18, 36, 48, 1953))
2025-07-03 19:36:48,004 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,005 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,006 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,006 - sqlalchemy.engine.Engine - INFO - [cached since 0.04905s ago] ('SUI', datetime.datetime(2025, 7, 3, 18, 36, 48, 5227))
2025-07-03 19:36:48,007 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,008 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,009 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,009 - sqlalchemy.engine.Engine - INFO - [cached since 0.04661s ago] ('SUI', datetime.datetime(2025, 7, 3, 18, 36, 48, 8103))
2025-07-03 19:36:48,010 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,011 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,011 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,011 - sqlalchemy.engine.Engine - INFO - [cached since 0.05461s ago] ('TAO', datetime.datetime(2025, 7, 3, 18, 36, 48, 10734))
2025-07-03 19:36:48,012 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,014 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,014 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,014 - sqlalchemy.engine.Engine - INFO - [cached since 0.05208s ago] ('TAO', datetime.datetime(2025, 7, 3, 18, 36, 48, 13540))
2025-07-03 19:36:48,015 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,016 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,017 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,017 - sqlalchemy.engine.Engine - INFO - [cached since 0.06023s ago] ('XRP', datetime.datetime(2025, 7, 3, 18, 36, 48, 16205))
2025-07-03 19:36:48,018 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,019 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,020 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,020 - sqlalchemy.engine.Engine - INFO - [cached since 0.05755s ago] ('XRP', datetime.datetime(2025, 7, 3, 18, 36, 48, 18975))
2025-07-03 19:36:48,021 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,022 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,022 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,022 - sqlalchemy.engine.Engine - INFO - [cached since 0.06522s ago] ('BUIDL', datetime.datetime(2025, 7, 3, 18, 36, 48, 21546))
2025-07-03 19:36:48,023 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,024 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,024 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,024 - sqlalchemy.engine.Engine - INFO - [cached since 0.06164s ago] ('BUIDL', datetime.datetime(2025, 7, 3, 18, 36, 48, 23550))
2025-07-03 19:36:48,026 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,028 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,028 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,029 - sqlalchemy.engine.Engine - INFO - [cached since 0.07168s ago] ('HYPE', datetime.datetime(2025, 7, 3, 18, 36, 48, 27122))
2025-07-03 19:36:48,029 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,031 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,032 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,032 - sqlalchemy.engine.Engine - INFO - [cached since 0.06964s ago] ('HYPE', datetime.datetime(2025, 7, 3, 18, 36, 48, 30277))
2025-07-03 19:36:48,033 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,034 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,034 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,035 - sqlalchemy.engine.Engine - INFO - [cached since 0.07767s ago] ('TRX', datetime.datetime(2025, 7, 3, 18, 36, 48, 33853))
2025-07-03 19:36:48,036 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,037 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,039 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,039 - sqlalchemy.engine.Engine - INFO - [cached since 0.07692s ago] ('TRX', datetime.datetime(2025, 7, 3, 18, 36, 48, 36717))
2025-07-03 19:36:48,040 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,041 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,042 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,042 - sqlalchemy.engine.Engine - INFO - [cached since 0.08485s ago] ('USDS', datetime.datetime(2025, 7, 3, 18, 36, 48, 41137))
2025-07-03 19:36:48,043 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,044 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,044 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,044 - sqlalchemy.engine.Engine - INFO - [cached since 0.08203s ago] ('USDS', datetime.datetime(2025, 7, 3, 18, 36, 48, 43750))
2025-07-03 19:36:48,045 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,046 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,046 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,046 - sqlalchemy.engine.Engine - INFO - [cached since 0.0896s ago] ('LEO', datetime.datetime(2025, 7, 3, 18, 36, 48, 45912))
2025-07-03 19:36:48,047 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,048 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,049 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,049 - sqlalchemy.engine.Engine - INFO - [cached since 0.08649s ago] ('LEO', datetime.datetime(2025, 7, 3, 18, 36, 48, 48152))
2025-07-03 19:36:48,049 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,051 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,051 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,052 - sqlalchemy.engine.Engine - INFO - [cached since 0.09466s ago] ('UNI', datetime.datetime(2025, 7, 3, 18, 36, 48, 50762))
2025-07-03 19:36:48,053 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,054 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,054 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,054 - sqlalchemy.engine.Engine - INFO - [cached since 0.09193s ago] ('UNI', datetime.datetime(2025, 7, 3, 18, 36, 48, 53518))
2025-07-03 19:36:48,055 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,056 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,056 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,056 - sqlalchemy.engine.Engine - INFO - [cached since 0.09937s ago] ('OKB', datetime.datetime(2025, 7, 3, 18, 36, 48, 55770))
2025-07-03 19:36:48,057 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,058 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,059 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,059 - sqlalchemy.engine.Engine - INFO - [cached since 0.09645s ago] ('OKB', datetime.datetime(2025, 7, 3, 18, 36, 48, 57958))
2025-07-03 19:36:48,059 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,061 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,061 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,061 - sqlalchemy.engine.Engine - INFO - [cached since 0.1041s ago] ('JITOSOL', datetime.datetime(2025, 7, 3, 18, 36, 48, 60458))
2025-07-03 19:36:48,062 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,063 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,063 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,063 - sqlalchemy.engine.Engine - INFO - [cached since 0.1009s ago] ('JITOSOL', datetime.datetime(2025, 7, 3, 18, 36, 48, 62675))
2025-07-03 19:36:48,064 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,065 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,065 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,066 - sqlalchemy.engine.Engine - INFO - [cached since 0.1087s ago] ('PEPE', datetime.datetime(2025, 7, 3, 18, 36, 48, 64763))
2025-07-03 19:36:48,067 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,068 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,068 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,068 - sqlalchemy.engine.Engine - INFO - [cached since 0.1059s ago] ('PEPE', datetime.datetime(2025, 7, 3, 18, 36, 48, 67459))
2025-07-03 19:36:48,069 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,070 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,070 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,070 - sqlalchemy.engine.Engine - INFO - [cached since 0.1134s ago] ('DOT', datetime.datetime(2025, 7, 3, 18, 36, 48, 69776))
2025-07-03 19:36:48,071 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,072 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,072 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,072 - sqlalchemy.engine.Engine - INFO - [cached since 0.1102s ago] ('DOT', datetime.datetime(2025, 7, 3, 18, 36, 48, 71801))
2025-07-03 19:36:48,073 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,076 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,076 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,076 - sqlalchemy.engine.Engine - INFO - [cached since 0.1191s ago] ('WBT', datetime.datetime(2025, 7, 3, 18, 36, 48, 75211))
2025-07-03 19:36:48,077 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,078 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,078 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,078 - sqlalchemy.engine.Engine - INFO - [cached since 0.1162s ago] ('WBT', datetime.datetime(2025, 7, 3, 18, 36, 48, 77687))
2025-07-03 19:36:48,079 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,080 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,080 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,080 - sqlalchemy.engine.Engine - INFO - [cached since 0.1236s ago] ('ADA', datetime.datetime(2025, 7, 3, 18, 36, 48, 80103))
2025-07-03 19:36:48,081 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,082 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,082 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,083 - sqlalchemy.engine.Engine - INFO - [cached since 0.1203s ago] ('ADA', datetime.datetime(2025, 7, 3, 18, 36, 48, 82060))
2025-07-03 19:36:48,083 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,084 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,085 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,085 - sqlalchemy.engine.Engine - INFO - [cached since 0.1281s ago] ('AVAX', datetime.datetime(2025, 7, 3, 18, 36, 48, 84364))
2025-07-03 19:36:48,086 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,087 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,087 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,087 - sqlalchemy.engine.Engine - INFO - [cached since 0.1249s ago] ('AVAX', datetime.datetime(2025, 7, 3, 18, 36, 48, 86614))
2025-07-03 19:36:48,088 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,089 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,089 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,089 - sqlalchemy.engine.Engine - INFO - [cached since 0.1323s ago] ('BCH', datetime.datetime(2025, 7, 3, 18, 36, 48, 88572))
2025-07-03 19:36:48,090 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,091 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,091 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,092 - sqlalchemy.engine.Engine - INFO - [cached since 0.1293s ago] ('BCH', datetime.datetime(2025, 7, 3, 18, 36, 48, 90931))
2025-07-03 19:36:48,092 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,093 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,094 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,094 - sqlalchemy.engine.Engine - INFO - [cached since 0.1369s ago] ('SHIB', datetime.datetime(2025, 7, 3, 18, 36, 48, 93160))
2025-07-03 19:36:48,094 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,095 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,096 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,096 - sqlalchemy.engine.Engine - INFO - [cached since 0.1334s ago] ('SHIB', datetime.datetime(2025, 7, 3, 18, 36, 48, 95335))
2025-07-03 19:36:48,096 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,097 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,098 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,098 - sqlalchemy.engine.Engine - INFO - [cached since 0.1408s ago] ('WEETH', datetime.datetime(2025, 7, 3, 18, 36, 48, 97263))
2025-07-03 19:36:48,098 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,100 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,100 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,100 - sqlalchemy.engine.Engine - INFO - [cached since 0.1378s ago] ('WEETH', datetime.datetime(2025, 7, 3, 18, 36, 48, 99507))
2025-07-03 19:36:48,101 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,102 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,102 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,102 - sqlalchemy.engine.Engine - INFO - [cached since 0.1454s ago] ('XLM', datetime.datetime(2025, 7, 3, 18, 36, 48, 101605))
2025-07-03 19:36:48,103 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,104 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,104 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,104 - sqlalchemy.engine.Engine - INFO - [cached since 0.142s ago] ('XLM', datetime.datetime(2025, 7, 3, 18, 36, 48, 103771))
2025-07-03 19:36:48,105 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,106 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,106 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,106 - sqlalchemy.engine.Engine - INFO - [cached since 0.1494s ago] ('ETC', datetime.datetime(2025, 7, 3, 18, 36, 48, 105734))
2025-07-03 19:36:48,107 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,108 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,108 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,108 - sqlalchemy.engine.Engine - INFO - [cached since 0.1461s ago] ('ETC', datetime.datetime(2025, 7, 3, 18, 36, 48, 107821))
2025-07-03 19:36:48,109 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,110 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,111 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,111 - sqlalchemy.engine.Engine - INFO - [cached since 0.1539s ago] ('NEAR', datetime.datetime(2025, 7, 3, 18, 36, 48, 109877))
2025-07-03 19:36:48,113 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,116 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,117 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,117 - sqlalchemy.engine.Engine - INFO - [cached since 0.1549s ago] ('NEAR', datetime.datetime(2025, 7, 3, 18, 36, 48, 114931))
2025-07-03 19:36:48,119 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,121 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,122 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,122 - sqlalchemy.engine.Engine - INFO - [cached since 0.1649s ago] ('PI', datetime.datetime(2025, 7, 3, 18, 36, 48, 120647))
2025-07-03 19:36:48,122 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,123 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,124 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,124 - sqlalchemy.engine.Engine - INFO - [cached since 0.1617s ago] ('PI', datetime.datetime(2025, 7, 3, 18, 36, 48, 123289))
2025-07-03 19:36:48,125 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,126 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,126 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,126 - sqlalchemy.engine.Engine - INFO - [cached since 0.1691s ago] ('WBTC', datetime.datetime(2025, 7, 3, 18, 36, 48, 125532))
2025-07-03 19:36:48,127 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,127 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,128 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,128 - sqlalchemy.engine.Engine - INFO - [cached since 0.1655s ago] ('WBTC', datetime.datetime(2025, 7, 3, 18, 36, 48, 127421))
2025-07-03 19:36:48,128 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,129 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,129 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,129 - sqlalchemy.engine.Engine - INFO - [cached since 0.1724s ago] ('SOL', datetime.datetime(2025, 7, 3, 18, 36, 48, 129020))
2025-07-03 19:36:48,130 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,131 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,131 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,131 - sqlalchemy.engine.Engine - INFO - [cached since 0.1688s ago] ('SOL', datetime.datetime(2025, 7, 3, 18, 36, 48, 130676))
2025-07-03 19:36:48,132 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,133 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,133 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,133 - sqlalchemy.engine.Engine - INFO - [cached since 0.1762s ago] ('ONDO', datetime.datetime(2025, 7, 3, 18, 36, 48, 132550))
2025-07-03 19:36:48,134 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,135 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,135 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,135 - sqlalchemy.engine.Engine - INFO - [cached since 0.1727s ago] ('ONDO', datetime.datetime(2025, 7, 3, 18, 36, 48, 134606))
2025-07-03 19:36:48,135 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,136 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,136 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,136 - sqlalchemy.engine.Engine - INFO - [cached since 0.1796s ago] ('BNB', datetime.datetime(2025, 7, 3, 18, 36, 48, 136263))
2025-07-03 19:36:48,137 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,138 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,138 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,138 - sqlalchemy.engine.Engine - INFO - [cached since 0.1756s ago] ('BNB', datetime.datetime(2025, 7, 3, 18, 36, 48, 137719))
2025-07-03 19:36:48,138 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,139 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,139 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,140 - sqlalchemy.engine.Engine - INFO - [cached since 0.1828s ago] ('USDT', datetime.datetime(2025, 7, 3, 18, 36, 48, 139114))
2025-07-03 19:36:48,140 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,141 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,141 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,141 - sqlalchemy.engine.Engine - INFO - [cached since 0.179s ago] ('USDT', datetime.datetime(2025, 7, 3, 18, 36, 48, 141111))
2025-07-03 19:36:48,142 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,143 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,143 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,143 - sqlalchemy.engine.Engine - INFO - [cached since 0.1861s ago] ('WSTETH', datetime.datetime(2025, 7, 3, 18, 36, 48, 142663))
2025-07-03 19:36:48,143 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,144 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,144 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,144 - sqlalchemy.engine.Engine - INFO - [cached since 0.1821s ago] ('WSTETH', datetime.datetime(2025, 7, 3, 18, 36, 48, 144194))
2025-07-03 19:36:48,145 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,146 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,146 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,146 - sqlalchemy.engine.Engine - INFO - [cached since 0.189s ago] ('HBAR', datetime.datetime(2025, 7, 3, 18, 36, 48, 145655))
2025-07-03 19:36:48,146 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,147 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,148 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,148 - sqlalchemy.engine.Engine - INFO - [cached since 0.1854s ago] ('HBAR', datetime.datetime(2025, 7, 3, 18, 36, 48, 147325))
2025-07-03 19:36:48,148 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,149 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,149 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,149 - sqlalchemy.engine.Engine - INFO - [cached since 0.1925s ago] ('TON', datetime.datetime(2025, 7, 3, 18, 36, 48, 149089))
2025-07-03 19:36:48,150 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,151 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,151 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,151 - sqlalchemy.engine.Engine - INFO - [cached since 0.189s ago] ('TON', datetime.datetime(2025, 7, 3, 18, 36, 48, 150816))
2025-07-03 19:36:48,152 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,153 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,153 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,153 - sqlalchemy.engine.Engine - INFO - [cached since 0.1962s ago] ('BGB', datetime.datetime(2025, 7, 3, 18, 36, 48, 152666))
2025-07-03 19:36:48,154 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,155 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,155 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,155 - sqlalchemy.engine.Engine - INFO - [cached since 0.1925s ago] ('BGB', datetime.datetime(2025, 7, 3, 18, 36, 48, 154497))
2025-07-03 19:36:48,155 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,156 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,156 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,156 - sqlalchemy.engine.Engine - INFO - [cached since 0.1993s ago] ('ICP', datetime.datetime(2025, 7, 3, 18, 36, 48, 155938))
2025-07-03 19:36:48,157 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,157 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,158 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,158 - sqlalchemy.engine.Engine - INFO - [cached since 0.1955s ago] ('ICP', datetime.datetime(2025, 7, 3, 18, 36, 48, 157469))
2025-07-03 19:36:48,158 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,159 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,159 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,159 - sqlalchemy.engine.Engine - INFO - [cached since 0.2026s ago] ('LINK', datetime.datetime(2025, 7, 3, 18, 36, 48, 159166))
2025-07-03 19:36:48,160 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,161 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,161 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,161 - sqlalchemy.engine.Engine - INFO - [cached since 0.1989s ago] ('LINK', datetime.datetime(2025, 7, 3, 18, 36, 48, 160815))
2025-07-03 19:36:48,162 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,162 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,162 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,163 - sqlalchemy.engine.Engine - INFO - [cached since 0.2057s ago] ('DAI', datetime.datetime(2025, 7, 3, 18, 36, 48, 162356))
2025-07-03 19:36:48,163 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,164 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,164 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,164 - sqlalchemy.engine.Engine - INFO - [cached since 0.2018s ago] ('DAI', datetime.datetime(2025, 7, 3, 18, 36, 48, 163754))
2025-07-03 19:36:48,165 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,165 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,166 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,166 - sqlalchemy.engine.Engine - INFO - [cached since 0.2089s ago] ('XMR', datetime.datetime(2025, 7, 3, 18, 36, 48, 165509))
2025-07-03 19:36:48,166 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,167 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,167 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,168 - sqlalchemy.engine.Engine - INFO - [cached since 0.2053s ago] ('XMR', datetime.datetime(2025, 7, 3, 18, 36, 48, 167206))
2025-07-03 19:36:48,168 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,169 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,169 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,169 - sqlalchemy.engine.Engine - INFO - [cached since 0.2121s ago] ('DOGE', datetime.datetime(2025, 7, 3, 18, 36, 48, 168750))
2025-07-03 19:36:48,169 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,170 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,170 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,171 - sqlalchemy.engine.Engine - INFO - [cached since 0.2084s ago] ('DOGE', datetime.datetime(2025, 7, 3, 18, 36, 48, 170232))
2025-07-03 19:36:48,171 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,172 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,172 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,172 - sqlalchemy.engine.Engine - INFO - [cached since 0.2154s ago] ('SUSDE', datetime.datetime(2025, 7, 3, 18, 36, 48, 172028))
2025-07-03 19:36:48,173 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,174 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,174 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,174 - sqlalchemy.engine.Engine - INFO - [cached since 0.2118s ago] ('SUSDE', datetime.datetime(2025, 7, 3, 18, 36, 48, 173621))
2025-07-03 19:36:48,175 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,175 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,175 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,175 - sqlalchemy.engine.Engine - INFO - [cached since 0.2186s ago] ('USDC', datetime.datetime(2025, 7, 3, 18, 36, 48, 175307))
2025-07-03 19:36:48,176 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,177 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,177 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,177 - sqlalchemy.engine.Engine - INFO - [cached since 0.2147s ago] ('USDC', datetime.datetime(2025, 7, 3, 18, 36, 48, 176808))
2025-07-03 19:36:48,177 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,178 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,178 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,178 - sqlalchemy.engine.Engine - INFO - [cached since 0.2213s ago] ('BSC-USD', datetime.datetime(2025, 7, 3, 18, 36, 48, 178036))
2025-07-03 19:36:48,178 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,179 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,179 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,179 - sqlalchemy.engine.Engine - INFO - [cached since 0.2171s ago] ('BSC-USD', datetime.datetime(2025, 7, 3, 18, 36, 48, 179229))
2025-07-03 19:36:48,180 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,180 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,180 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,180 - sqlalchemy.engine.Engine - INFO - [cached since 0.2236s ago] ('LTC', datetime.datetime(2025, 7, 3, 18, 36, 48, 180421))
2025-07-03 19:36:48,181 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,181 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,182 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,182 - sqlalchemy.engine.Engine - INFO - [cached since 0.2196s ago] ('LTC', datetime.datetime(2025, 7, 3, 18, 36, 48, 181539))
2025-07-03 19:36:48,182 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,183 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,183 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,183 - sqlalchemy.engine.Engine - INFO - [cached since 0.2264s ago] ('STETH', datetime.datetime(2025, 7, 3, 18, 36, 48, 183050))
2025-07-03 19:36:48,184 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,185 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,185 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,185 - sqlalchemy.engine.Engine - INFO - [cached since 0.2228s ago] ('STETH', datetime.datetime(2025, 7, 3, 18, 36, 48, 184693))
2025-07-03 19:36:48,185 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,186 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,186 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:36:48,187 - sqlalchemy.engine.Engine - INFO - [cached since 0.2296s ago] ('ETH', datetime.datetime(2025, 7, 3, 18, 36, 48, 186262))
2025-07-03 19:36:48,187 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:48,188 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:48,188 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:36:48,188 - sqlalchemy.engine.Engine - INFO - [cached since 0.2258s ago] ('ETH', datetime.datetime(2025, 7, 3, 18, 36, 48, 187881))
2025-07-03 19:36:48,189 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:58,463 - app.services.data_collector - INFO - Starting data collection service
2025-07-03 19:36:59,645 - app.services.data_collector - INFO - Monitoring 50 symbols: ['BTC', 'ETH', 'USDC', 'SOL', 'FDUSD', 'XRP', 'PEPE', 'DOGE', 'SUI', 'WIF']...
2025-07-03 19:36:59,646 - app.services.data_collector - INFO - Starting Binance WebSocket data collection
2025-07-03 19:36:59,672 - app.services.data_collector - INFO - Starting CoinGecko data collection
2025-07-03 19:36:59,672 - app.services.data_collector - INFO - Starting Reddit data collection
2025-07-03 19:36:59,673 - app.services.data_collector - INFO - Starting health check loop
2025-07-03 19:36:59,673 - app.services.data_collector - WARNING - Binance WebSocket disconnected
2025-07-03 19:36:59,674 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:36:59,675 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:36:59,675 - sqlalchemy.engine.Engine - INFO - [cached since 23.7s ago] ()
2025-07-03 19:36:59,676 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:36:59,676 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:37:00,324 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:00,331 - sqlalchemy.engine.Engine - INFO - INSERT INTO market_data (time, symbol, price, volume, market_cap, source) VALUES ($1::TIMESTAMP WITH TIME ZONE, $2::VARCHAR, $3::NUMERIC(20, 8), $4::NUMERIC(20, 8), $5::NUMERIC(20, 2), $6::VARCHAR), ($7::TIMESTAMP WITH TIME ZONE, $8::VARCHAR, $9::NUMERIC(20, 8), $10::NUMERIC(20, 8), $11::NUMERIC(20, 2), $12::VARCHAR), ($13::TIMESTAMP WITH TIME ZONE, $14::VARCHAR, $15::NUMERIC(20, 8), $16::NUMERIC(20, 8), $17::NUMERIC(20, 2), $18::VARCHAR), ($19::TIMESTAMP WITH TIME ZONE, $20::VARCHAR, $21::NUMERIC(20, 8), $22::NUMERIC(20, 8), $23::NUMERIC(20, 2), $24::VARCHAR), ($25::TIMESTAMP WITH TIME ZONE, $26::VARCHAR, $27::NUMERIC(20, 8), $28::NUMERIC(20, 8), $29::NUMERIC(20, 2), $30::VARCHAR), ($31::TIMESTAMP WITH TIME ZONE, $32::VARCHAR, $33::NUMERIC(20, 8), $34::NUMERIC(20, 8), $35::NUMERIC(20, 2), $36::VARCHAR), ($37::TIMESTAMP WITH TIME ZONE, $38::VARCHAR, $39::NUMERIC(20, 8), $40::NUMERIC(20, 8), $41::NUMERIC(20, 2), $42::VARCHAR), ($43::TIMESTAMP WITH TIME ZONE, $44::VARCHAR, $45::NUMERIC(20, 8), $46::NUMERIC(20, 8), $47::NUMERIC(20, 2), $48::VARCHAR), ($49::TIMESTAMP WITH TIME ZONE, $50::VARCHAR, $51::NUMERIC(20, 8), $52::NUMERIC(20, 8), $53::NUMERIC(20, 2), $54::VARCHAR), ($55::TIMESTAMP WITH TIME ZONE, $56::VARCHAR, $57::NUMERIC(20, 8), $58::NUMERIC(20, 8), $59::NUMERIC(20, 2), $60::VARCHAR), ($61::TIMESTAMP WITH TIME ZONE, $62::VARCHAR, $63::NUMERIC(20, 8), $64::NUMERIC(20, 8), $65::NUMERIC(20, 2), $66::VARCHAR), ($67::TIMESTAMP WITH TIME ZONE, $68::VARCHAR, $69::NUMERIC(20, 8), $70::NUMERIC(20, 8), $71::NUMERIC(20, 2), $72::VARCHAR), ($73::TIMESTAMP WITH TIME ZONE, $74::VARCHAR, $75::NUMERIC(20, 8), $76::NUMERIC(20, 8), $77::NUMERIC(20, 2), $78::VARCHAR), ($79::TIMESTAMP WITH TIME ZONE, $80::VARCHAR, $81::NUMERIC(20, 8), $82::NUMERIC(20, 8), $83::NUMERIC(20, 2), $84::VARCHAR), ($85::TIMESTAMP WITH TIME ZONE, $86::VARCHAR, $87::NUMERIC(20, 8), $88::NUMERIC(20, 8), $89::NUMERIC(20, 2), $90::VARCHAR), ($91::TIMESTAMP WITH TIME ZONE, $92::VARCHAR, $93::NUMERIC(20, 8), $94::NUMERIC(20, 8), $95::NUMERIC(20, 2), $96::VARCHAR), ($97::TIMESTAMP WITH TIME ZONE, $98::VARCHAR, $99::NUMERIC(20, 8), $100::NUMERIC(20, 8), $101::NUMERIC(20, 2), $102::VARCHAR), ($103::TIMESTAMP WITH TIME ZONE, $104::VARCHAR, $105::NUMERIC(20, 8), $106::NUMERIC(20, 8), $107::NUMERIC(20, 2), $108::VARCHAR), ($109::TIMESTAMP WITH TIME ZONE, $110::VARCHAR, $111::NUMERIC(20, 8), $112::NUMERIC(20, 8), $113::NUMERIC(20, 2), $114::VARCHAR), ($115::TIMESTAMP WITH TIME ZONE, $116::VARCHAR, $117::NUMERIC(20, 8), $118::NUMERIC(20, 8), $119::NUMERIC(20, 2), $120::VARCHAR), ($121::TIMESTAMP WITH TIME ZONE, $122::VARCHAR, $123::NUMERIC(20, 8), $124::NUMERIC(20, 8), $125::NUMERIC(20, 2), $126::VARCHAR), ($127::TIMESTAMP WITH TIME ZONE, $128::VARCHAR, $129::NUMERIC(20, 8), $130::NUMERIC(20, 8), $131::NUMERIC(20, 2), $132::VARCHAR), ($133::TIMESTAMP WITH TIME ZONE, $134::VARCHAR, $135::NUMERIC(20, 8), $136::NUMERIC(20, 8), $137::NUMERIC(20, 2), $138::VARCHAR), ($139::TIMESTAMP WITH TIME ZONE, $140::VARCHAR, $141::NUMERIC(20, 8), $142::NUMERIC(20, 8), $143::NUMERIC(20, 2), $144::VARCHAR), ($145::TIMESTAMP WITH TIME ZONE, $146::VARCHAR, $147::NUMERIC(20, 8), $148::NUMERIC(20, 8), $149::NUMERIC(20, 2), $150::VARCHAR), ($151::TIMESTAMP WITH TIME ZONE, $152::VARCHAR, $153::NUMERIC(20, 8), $154::NUMERIC(20, 8), $155::NUMERIC(20, 2), $156::VARCHAR), ($157::TIMESTAMP WITH TIME ZONE, $158::VARCHAR, $159::NUMERIC(20, 8), $160::NUMERIC(20, 8), $161::NUMERIC(20, 2), $162::VARCHAR), ($163::TIMESTAMP WITH TIME ZONE, $164::VARCHAR, $165::NUMERIC(20, 8), $166::NUMERIC(20, 8), $167::NUMERIC(20, 2), $168::VARCHAR), ($169::TIMESTAMP WITH TIME ZONE, $170::VARCHAR, $171::NUMERIC(20, 8), $172::NUMERIC(20, 8), $173::NUMERIC(20, 2), $174::VARCHAR), ($175::TIMESTAMP WITH TIME ZONE, $176::VARCHAR, $177::NUMERIC(20, 8), $178::NUMERIC(20, 8), $179::NUMERIC(20, 2), $180::VARCHAR), ($181::TIMESTAMP WITH TIME ZONE, $182::VARCHAR, $183::NUMERIC(20, 8), $184::NUMERIC(20, 8), $185::NUMERIC(20, 2), $186::VARCHAR), ($187::TIMESTAMP WITH TIME ZONE, $188::VARCHAR, $189::NUMERIC(20, 8), $190::NUMERIC(20, 8), $191::NUMERIC(20, 2), $192::VARCHAR), ($193::TIMESTAMP WITH TIME ZONE, $194::VARCHAR, $195::NUMERIC(20, 8), $196::NUMERIC(20, 8), $197::NUMERIC(20, 2), $198::VARCHAR), ($199::TIMESTAMP WITH TIME ZONE, $200::VARCHAR, $201::NUMERIC(20, 8), $202::NUMERIC(20, 8), $203::NUMERIC(20, 2), $204::VARCHAR), ($205::TIMESTAMP WITH TIME ZONE, $206::VARCHAR, $207::NUMERIC(20, 8), $208::NUMERIC(20, 8), $209::NUMERIC(20, 2), $210::VARCHAR), ($211::TIMESTAMP WITH TIME ZONE, $212::VARCHAR, $213::NUMERIC(20, 8), $214::NUMERIC(20, 8), $215::NUMERIC(20, 2), $216::VARCHAR), ($217::TIMESTAMP WITH TIME ZONE, $218::VARCHAR, $219::NUMERIC(20, 8), $220::NUMERIC(20, 8), $221::NUMERIC(20, 2), $222::VARCHAR), ($223::TIMESTAMP WITH TIME ZONE, $224::VARCHAR, $225::NUMERIC(20, 8), $226::NUMERIC(20, 8), $227::NUMERIC(20, 2), $228::VARCHAR), ($229::TIMESTAMP WITH TIME ZONE, $230::VARCHAR, $231::NUMERIC(20, 8), $232::NUMERIC(20, 8), $233::NUMERIC(20, 2), $234::VARCHAR), ($235::TIMESTAMP WITH TIME ZONE, $236::VARCHAR, $237::NUMERIC(20, 8), $238::NUMERIC(20, 8), $239::NUMERIC(20, 2), $240::VARCHAR), ($241::TIMESTAMP WITH TIME ZONE, $242::VARCHAR, $243::NUMERIC(20, 8), $244::NUMERIC(20, 8), $245::NUMERIC(20, 2), $246::VARCHAR), ($247::TIMESTAMP WITH TIME ZONE, $248::VARCHAR, $249::NUMERIC(20, 8), $250::NUMERIC(20, 8), $251::NUMERIC(20, 2), $252::VARCHAR), ($253::TIMESTAMP WITH TIME ZONE, $254::VARCHAR, $255::NUMERIC(20, 8), $256::NUMERIC(20, 8), $257::NUMERIC(20, 2), $258::VARCHAR), ($259::TIMESTAMP WITH TIME ZONE, $260::VARCHAR, $261::NUMERIC(20, 8), $262::NUMERIC(20, 8), $263::NUMERIC(20, 2), $264::VARCHAR), ($265::TIMESTAMP WITH TIME ZONE, $266::VARCHAR, $267::NUMERIC(20, 8), $268::NUMERIC(20, 8), $269::NUMERIC(20, 2), $270::VARCHAR), ($271::TIMESTAMP WITH TIME ZONE, $272::VARCHAR, $273::NUMERIC(20, 8), $274::NUMERIC(20, 8), $275::NUMERIC(20, 2), $276::VARCHAR), ($277::TIMESTAMP WITH TIME ZONE, $278::VARCHAR, $279::NUMERIC(20, 8), $280::NUMERIC(20, 8), $281::NUMERIC(20, 2), $282::VARCHAR), ($283::TIMESTAMP WITH TIME ZONE, $284::VARCHAR, $285::NUMERIC(20, 8), $286::NUMERIC(20, 8), $287::NUMERIC(20, 2), $288::VARCHAR), ($289::TIMESTAMP WITH TIME ZONE, $290::VARCHAR, $291::NUMERIC(20, 8), $292::NUMERIC(20, 8), $293::NUMERIC(20, 2), $294::VARCHAR), ($295::TIMESTAMP WITH TIME ZONE, $296::VARCHAR, $297::NUMERIC(20, 8), $298::NUMERIC(20, 8), $299::NUMERIC(20, 2), $300::VARCHAR)
2025-07-03 19:37:00,331 - sqlalchemy.engine.Engine - INFO - [no key 0.00129s] (datetime.datetime(2025, 7, 3, 19, 37, 0, 318184), 'BTC', 109836, None, 2185605903558, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318209), 'ETH', 2588.27, None, 312637717553, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318212), 'USDT', 1.0, None, 158319990552, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318214), 'XRP', 2.28, None, 134623642954, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318216), 'BNB', 661.78, None, 96547919475, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318219), 'SOL', 151.72, None, 81184921376, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318221), 'USDC', 0.999874, None, 62061550234, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318223), 'TRX', 0.285701, None, 27082768948, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318225), 'DOGE' ... 200 parameters truncated ... 3020666049, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318325), 'OKB', 50.02, None, 3001197494, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318327), 'BUIDL', 1.0, None, 2846195399, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318329), 'NEAR', 2.27, None, 2797762072, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318330), 'JITOSOL', 184.32, None, 2709988529, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318353), 'ICP', 5.01, None, 2682081309, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318354), 'ETC', 17.02, None, 2599596975, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318355), 'CRO', 0.082952, None, 2581203492, 'coingecko', datetime.datetime(2025, 7, 3, 19, 37, 0, 318357), 'ONDO', 0.79498, None, 2511508428, 'coingecko')
2025-07-03 19:37:00,336 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:37:00,337 - app.services.data_storage - INFO - Stored batch of 50 market data points
2025-07-03 19:37:00,339 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:00,340 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:37:00,341 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ('coingecko',)
2025-07-03 19:37:00,343 - sqlalchemy.engine.Engine - INFO - UPDATE data_sources SET last_successful_fetch=$1::TIMESTAMP WITH TIME ZONE, updated_at=now() WHERE data_sources.id = $2::UUID
2025-07-03 19:37:00,344 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] (datetime.datetime(2025, 7, 3, 19, 37, 0, 342625), UUID('5783138a-4a1e-4a5b-960b-61aab5f37bae'))
2025-07-03 19:37:00,345 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:37:00,346 - app.services.data_collector - INFO - Stored 50 CoinGecko data points
2025-07-03 19:37:00,381 - app.data_sources.reddit_client - ERROR - Failed to get Reddit access token: 401
2025-07-03 19:37:00,921 - app.data_sources.binance_client - INFO - Connected to Binance WebSocket
2025-07-03 19:37:00,922 - app.data_sources.binance_client - INFO - Subscribed to ticker data for 50 symbols
2025-07-03 19:37:08,153 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:08,153 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:37:08,153 - sqlalchemy.engine.Engine - INFO - [cached since 20.2s ago] ('BTC', datetime.datetime(2025, 7, 3, 18, 37, 8, 151550))
2025-07-03 19:37:08,154 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:37:08,155 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:08,155 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:37:08,155 - sqlalchemy.engine.Engine - INFO - [cached since 20.19s ago] ('BTC', datetime.datetime(2025, 7, 3, 18, 37, 8, 155010))
2025-07-03 19:37:08,156 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:37:20,424 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:20,425 - sqlalchemy.engine.Engine - INFO - SELECT data_sources.id, data_sources.name, data_sources.source_type, data_sources.is_active, data_sources.last_successful_fetch, data_sources.last_error, data_sources.error_count, data_sources.requests_per_minute, data_sources.current_usage, data_sources.created_at, data_sources.updated_at 
FROM data_sources 
WHERE data_sources.name = $1::VARCHAR
2025-07-03 19:37:20,425 - sqlalchemy.engine.Engine - INFO - [cached since 20.09s ago] ('reddit',)
2025-07-03 19:37:20,427 - sqlalchemy.engine.Engine - INFO - INSERT INTO data_sources (id, name, source_type, is_active, last_successful_fetch, last_error, error_count, requests_per_minute, current_usage, updated_at) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::BOOLEAN, $5::TIMESTAMP WITH TIME ZONE, $6::VARCHAR, $7::INTEGER, $8::INTEGER, $9::INTEGER, $10::TIMESTAMP WITH TIME ZONE) RETURNING data_sources.created_at
2025-07-03 19:37:20,428 - sqlalchemy.engine.Engine - INFO - [generated in 0.00032s] (UUID('2acf9de8-6230-4f71-9f80-220fb5ef237e'), 'reddit', 'api', True, datetime.datetime(2025, 7, 3, 19, 37, 20, 426924), None, 0, None, 0, None)
2025-07-03 19:37:20,429 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:37:37,163 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:37,164 - sqlalchemy.engine.Engine - INFO - SELECT correlations.id, correlations.symbol, correlations.pattern_type, correlations.confidence_score, correlations.trigger_time, correlations.detection_time, correlations.evidence, correlations.status, correlations.price_change_1h, correlations.price_change_24h, correlations.created_at, correlations.updated_at 
FROM correlations 
WHERE correlations.trigger_time >= $1::TIMESTAMP WITH TIME ZONE AND correlations.status = $2::VARCHAR ORDER BY correlations.trigger_time DESC
2025-07-03 19:37:37,164 - sqlalchemy.engine.Engine - INFO - [cached since 61.19s ago] (datetime.datetime(2025, 7, 2, 19, 37, 37, 161266), 'active')
2025-07-03 19:37:37,166 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:37:37,167 - app.services.pattern_service - ERROR - Error getting recent patterns: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column correlations.detection_time does not exist
[SQL: SELECT correlations.id, correlations.symbol, correlations.pattern_type, correlations.confidence_score, correlations.trigger_time, correlations.detection_time, correlations.evidence, correlations.status, correlations.price_change_1h, correlations.price_change_24h, correlations.created_at, correlations.updated_at 
FROM correlations 
WHERE correlations.trigger_time >= $1::TIMESTAMP WITH TIME ZONE AND correlations.status = $2::VARCHAR ORDER BY correlations.trigger_time DESC]
[parameters: (datetime.datetime(2025, 7, 2, 19, 37, 37, 161266), 'active')]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-03 19:37:37,168 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:37,169 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:37:37,169 - sqlalchemy.engine.Engine - INFO - [cached since 61.19s ago] ()
2025-07-03 19:37:37,170 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:37:47,341 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:47,341 - sqlalchemy.engine.Engine - INFO - SELECT market_data.time, market_data.symbol, market_data.price, market_data.volume, market_data.market_cap, market_data.source, market_data.created_at 
FROM market_data 
WHERE market_data.symbol = $1::VARCHAR AND market_data.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY market_data.time DESC
2025-07-03 19:37:47,342 - sqlalchemy.engine.Engine - INFO - [cached since 59.38s ago] ('ETH', datetime.datetime(2025, 7, 3, 18, 37, 47, 339182))
2025-07-03 19:37:47,343 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:37:47,344 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:47,344 - sqlalchemy.engine.Engine - INFO - SELECT social_mentions.time, social_mentions.mention_id, social_mentions.symbol, social_mentions.platform, social_mentions.account, social_mentions.content, social_mentions.engagement_score, social_mentions.sentiment_score, social_mentions.source_url, social_mentions.created_at 
FROM social_mentions 
WHERE social_mentions.symbol = $1::VARCHAR AND social_mentions.time >= $2::TIMESTAMP WITH TIME ZONE ORDER BY social_mentions.time DESC
2025-07-03 19:37:47,344 - sqlalchemy.engine.Engine - INFO - [cached since 59.38s ago] ('ETH', datetime.datetime(2025, 7, 3, 18, 37, 47, 343523))
2025-07-03 19:37:47,345 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:37:57,425 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:57,425 - sqlalchemy.engine.Engine - INFO - SELECT correlations.id, correlations.symbol, correlations.pattern_type, correlations.confidence_score, correlations.trigger_time, correlations.detection_time, correlations.evidence, correlations.status, correlations.price_change_1h, correlations.price_change_24h, correlations.created_at, correlations.updated_at 
FROM correlations 
WHERE correlations.trigger_time >= $1::TIMESTAMP WITH TIME ZONE AND correlations.status = $2::VARCHAR ORDER BY correlations.trigger_time DESC
2025-07-03 19:37:57,426 - sqlalchemy.engine.Engine - INFO - [cached since 81.45s ago] (datetime.datetime(2025, 7, 3, 18, 37, 57, 424169), 'active')
2025-07-03 19:37:57,426 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:37:57,427 - app.services.pattern_service - ERROR - Error getting recent patterns: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column correlations.detection_time does not exist
[SQL: SELECT correlations.id, correlations.symbol, correlations.pattern_type, correlations.confidence_score, correlations.trigger_time, correlations.detection_time, correlations.evidence, correlations.status, correlations.price_change_1h, correlations.price_change_24h, correlations.created_at, correlations.updated_at 
FROM correlations 
WHERE correlations.trigger_time >= $1::TIMESTAMP WITH TIME ZONE AND correlations.status = $2::VARCHAR ORDER BY correlations.trigger_time DESC]
[parameters: (datetime.datetime(2025, 7, 3, 18, 37, 57, 424169), 'active')]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-03 19:37:59,686 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:37:59,687 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:37:59,688 - sqlalchemy.engine.Engine - INFO - [cached since 83.71s ago] ()
2025-07-03 19:37:59,690 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:37:59,691 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:38:59,699 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:38:59,701 - sqlalchemy.engine.Engine - INFO - SELECT DISTINCT market_data.symbol 
FROM market_data
2025-07-03 19:38:59,701 - sqlalchemy.engine.Engine - INFO - [cached since 143.7s ago] ()
2025-07-03 19:38:59,703 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-03 19:38:59,704 - app.services.data_collector - INFO - Currently tracking 50 symbols with data
2025-07-03 19:39:31,024 - main - INFO - Shutting down Nexus application...
2025-07-03 19:39:41,904 - main - INFO - Starting Nexus application...
2025-07-03 19:39:41,928 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:39:41,928 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:39:41,929 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:39:41,929 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:39:41,930 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:39:41,930 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:39:41,931 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:39:41,932 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:39:41,932 - sqlalchemy.engine.Engine - INFO - [generated in 0.00026s] ()
2025-07-03 19:39:41,933 - app.core.database - INFO - Database connection successful
2025-07-03 19:39:41,933 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:39:41,933 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:39:41,933 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:39:41,935 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:39:41,935 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:39:41,938 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:39:41,938 - sqlalchemy.engine.Engine - INFO - [cached since 0.002541s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:39:41,938 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:39:41,938 - sqlalchemy.engine.Engine - INFO - [cached since 0.003148s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:39:41,939 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:39:41,939 - sqlalchemy.engine.Engine - INFO - [cached since 0.003661s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:39:41,939 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:39:41,939 - sqlalchemy.engine.Engine - INFO - [cached since 0.004271s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:39:41,940 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:39:41,940 - sqlalchemy.engine.Engine - INFO - [cached since 0.004949s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:39:41,941 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:39:41,941 - sqlalchemy.engine.Engine - INFO - [cached since 0.005579s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:39:41,941 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:39:41,941 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:39:41,941 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:39:41,942 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:39:41,942 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:39:41,942 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:39:41,943 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:39:41,943 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:39:41,943 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:39:41,944 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:39:41,944 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:39:41,944 - main - INFO - Database initialized successfully
2025-07-03 19:40:41,931 - main - INFO - Shutting down Nexus application...
2025-07-03 19:40:42,493 - main - INFO - Starting Nexus application...
2025-07-03 19:40:42,517 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:40:42,517 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:40:42,518 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:40:42,519 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:40:42,520 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:40:42,520 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:40:42,521 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:40:42,521 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:40:42,521 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ()
2025-07-03 19:40:42,522 - app.core.database - INFO - Database connection successful
2025-07-03 19:40:42,522 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:40:42,522 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:40:42,523 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:40:42,524 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:40:42,524 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:40:42,527 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:40:42,527 - sqlalchemy.engine.Engine - INFO - [cached since 0.002661s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:40:42,527 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:40:42,528 - sqlalchemy.engine.Engine - INFO - [cached since 0.003391s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:40:42,528 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:40:42,528 - sqlalchemy.engine.Engine - INFO - [cached since 0.004078s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:40:42,529 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:40:42,529 - sqlalchemy.engine.Engine - INFO - [cached since 0.004706s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:40:42,529 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:40:42,530 - sqlalchemy.engine.Engine - INFO - [cached since 0.005318s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:40:42,530 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:40:42,530 - sqlalchemy.engine.Engine - INFO - [cached since 0.005909s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:40:42,531 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:40:42,531 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:40:42,531 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:40:42,531 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:40:42,532 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:40:42,532 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:40:42,532 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:40:42,532 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:40:42,533 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:40:42,533 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:40:42,533 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:40:42,534 - main - INFO - Database initialized successfully
2025-07-03 19:41:12,249 - main - INFO - Shutting down Nexus application...
2025-07-03 19:41:12,805 - main - INFO - Starting Nexus application...
2025-07-03 19:41:12,829 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:41:12,829 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:12,830 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:41:12,830 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:12,831 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:41:12,831 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:12,832 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:41:12,832 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:41:12,833 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] ()
2025-07-03 19:41:12,833 - app.core.database - INFO - Database connection successful
2025-07-03 19:41:12,833 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:41:12,833 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:41:12,834 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:41:12,836 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:12,836 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:12,838 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:12,838 - sqlalchemy.engine.Engine - INFO - [cached since 0.002581s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:12,839 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:12,839 - sqlalchemy.engine.Engine - INFO - [cached since 0.003233s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:12,839 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:12,839 - sqlalchemy.engine.Engine - INFO - [cached since 0.003761s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:12,840 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:12,840 - sqlalchemy.engine.Engine - INFO - [cached since 0.004292s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:12,840 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:12,840 - sqlalchemy.engine.Engine - INFO - [cached since 0.004771s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:12,841 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:12,841 - sqlalchemy.engine.Engine - INFO - [cached since 0.005339s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:12,841 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:41:12,841 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:41:12,841 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:41:12,842 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:41:12,842 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:41:12,842 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:41:12,843 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:41:12,843 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:41:12,843 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:41:12,844 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:41:12,844 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:41:12,845 - main - INFO - Database initialized successfully
2025-07-03 19:41:38,656 - main - INFO - Shutting down Nexus application...
2025-07-03 19:41:39,211 - main - INFO - Starting Nexus application...
2025-07-03 19:41:39,235 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:41:39,235 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:39,236 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:41:39,236 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:39,237 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:41:39,237 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:39,238 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:41:39,239 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:41:39,239 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ()
2025-07-03 19:41:39,239 - app.core.database - INFO - Database connection successful
2025-07-03 19:41:39,240 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:41:39,240 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:41:39,240 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:41:39,242 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:39,242 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:39,244 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:39,244 - sqlalchemy.engine.Engine - INFO - [cached since 0.0027s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:39,245 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:39,245 - sqlalchemy.engine.Engine - INFO - [cached since 0.003427s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:39,246 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:39,246 - sqlalchemy.engine.Engine - INFO - [cached since 0.004279s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:39,247 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:39,247 - sqlalchemy.engine.Engine - INFO - [cached since 0.005034s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:39,247 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:39,247 - sqlalchemy.engine.Engine - INFO - [cached since 0.005669s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:39,248 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:39,248 - sqlalchemy.engine.Engine - INFO - [cached since 0.006377s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:39,249 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:41:39,249 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:41:39,249 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:41:39,250 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:41:39,250 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:41:39,250 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:41:39,251 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:41:39,251 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:41:39,251 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:41:39,251 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:41:39,252 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:41:39,252 - main - INFO - Database initialized successfully
2025-07-03 19:41:55,909 - main - INFO - Shutting down Nexus application...
2025-07-03 19:41:56,434 - main - INFO - Starting Nexus application...
2025-07-03 19:41:56,457 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:41:56,457 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:56,458 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:41:56,458 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:56,459 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:41:56,459 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:41:56,460 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:41:56,461 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:41:56,461 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ()
2025-07-03 19:41:56,461 - app.core.database - INFO - Database connection successful
2025-07-03 19:41:56,462 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:41:56,462 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:41:56,462 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:41:56,464 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:56,464 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:56,466 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:56,466 - sqlalchemy.engine.Engine - INFO - [cached since 0.002551s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:56,467 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:56,467 - sqlalchemy.engine.Engine - INFO - [cached since 0.003102s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:56,467 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:56,467 - sqlalchemy.engine.Engine - INFO - [cached since 0.003637s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:56,468 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:56,468 - sqlalchemy.engine.Engine - INFO - [cached since 0.004232s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:56,468 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:56,469 - sqlalchemy.engine.Engine - INFO - [cached since 0.004889s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:56,469 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:41:56,469 - sqlalchemy.engine.Engine - INFO - [cached since 0.005704s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:41:56,470 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:41:56,470 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:41:56,470 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:41:56,471 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:41:56,471 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:41:56,471 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:41:56,471 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:41:56,472 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:41:56,472 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:41:56,472 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:41:56,472 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:41:56,473 - main - INFO - Database initialized successfully
2025-07-03 19:42:11,347 - main - INFO - Shutting down Nexus application...
2025-07-03 19:42:11,879 - main - INFO - Starting Nexus application...
2025-07-03 19:42:11,903 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:42:11,903 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:42:11,904 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:42:11,904 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:42:11,905 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:42:11,905 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:42:11,906 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:42:11,906 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:42:11,907 - sqlalchemy.engine.Engine - INFO - [generated in 0.00031s] ()
2025-07-03 19:42:11,907 - app.core.database - INFO - Database connection successful
2025-07-03 19:42:11,908 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:42:11,908 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:42:11,908 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:42:11,910 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:11,911 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:11,913 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:11,913 - sqlalchemy.engine.Engine - INFO - [cached since 0.002806s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:11,914 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:11,914 - sqlalchemy.engine.Engine - INFO - [cached since 0.003558s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:11,914 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:11,914 - sqlalchemy.engine.Engine - INFO - [cached since 0.004162s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:11,915 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:11,915 - sqlalchemy.engine.Engine - INFO - [cached since 0.004788s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:11,916 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:11,916 - sqlalchemy.engine.Engine - INFO - [cached since 0.005454s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:11,916 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:11,916 - sqlalchemy.engine.Engine - INFO - [cached since 0.006203s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:11,917 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:42:11,917 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:42:11,917 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:42:11,918 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:42:11,918 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:42:11,918 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:42:11,919 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:42:11,919 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:42:11,919 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:42:11,920 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:42:11,920 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:42:11,920 - main - INFO - Database initialized successfully
2025-07-03 19:42:41,498 - main - INFO - Shutting down Nexus application...
2025-07-03 19:42:42,057 - main - INFO - Starting Nexus application...
2025-07-03 19:42:42,081 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:42:42,081 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:42:42,082 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:42:42,082 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:42:42,083 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:42:42,083 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:42:42,084 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:42:42,084 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:42:42,084 - sqlalchemy.engine.Engine - INFO - [generated in 0.00026s] ()
2025-07-03 19:42:42,085 - app.core.database - INFO - Database connection successful
2025-07-03 19:42:42,085 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:42:42,086 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:42:42,086 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:42:42,088 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:42,088 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:42,090 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:42,091 - sqlalchemy.engine.Engine - INFO - [cached since 0.002542s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:42,091 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:42,091 - sqlalchemy.engine.Engine - INFO - [cached since 0.003124s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:42,092 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:42,092 - sqlalchemy.engine.Engine - INFO - [cached since 0.003655s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:42,092 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:42,092 - sqlalchemy.engine.Engine - INFO - [cached since 0.0043s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:42,093 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:42,093 - sqlalchemy.engine.Engine - INFO - [cached since 0.004831s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:42,093 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:42:42,093 - sqlalchemy.engine.Engine - INFO - [cached since 0.005406s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:42:42,094 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:42:42,094 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:42:42,094 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ()
2025-07-03 19:42:42,095 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:42:42,095 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:42:42,095 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:42:42,096 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:42:42,096 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:42:42,096 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:42:42,097 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:42:42,097 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:42:42,098 - main - INFO - Database initialized successfully
2025-07-03 19:43:16,256 - main - INFO - Shutting down Nexus application...
2025-07-03 19:43:16,789 - main - INFO - Starting Nexus application...
2025-07-03 19:43:16,813 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:43:16,813 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:43:16,814 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:43:16,815 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:43:16,815 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:43:16,816 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:43:16,816 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:43:16,817 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:43:16,817 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ()
2025-07-03 19:43:16,817 - app.core.database - INFO - Database connection successful
2025-07-03 19:43:16,818 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:43:16,818 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:43:16,818 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:43:16,820 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:16,820 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:16,822 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:16,823 - sqlalchemy.engine.Engine - INFO - [cached since 0.002839s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:16,823 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:16,823 - sqlalchemy.engine.Engine - INFO - [cached since 0.003435s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:16,824 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:16,824 - sqlalchemy.engine.Engine - INFO - [cached since 0.004018s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:16,824 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:16,824 - sqlalchemy.engine.Engine - INFO - [cached since 0.004583s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:16,825 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:16,825 - sqlalchemy.engine.Engine - INFO - [cached since 0.00509s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:16,825 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:16,825 - sqlalchemy.engine.Engine - INFO - [cached since 0.005649s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:16,826 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:43:16,826 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:43:16,826 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:43:16,827 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:43:16,827 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:43:16,827 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ()
2025-07-03 19:43:16,828 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:43:16,828 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:43:16,828 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ()
2025-07-03 19:43:16,829 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:43:16,829 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:43:16,829 - main - INFO - Database initialized successfully
2025-07-03 19:43:55,690 - main - INFO - Shutting down Nexus application...
2025-07-03 19:43:56,225 - main - INFO - Starting Nexus application...
2025-07-03 19:43:56,249 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:43:56,250 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:43:56,251 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:43:56,251 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:43:56,252 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:43:56,252 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:43:56,253 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:43:56,253 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:43:56,253 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ()
2025-07-03 19:43:56,254 - app.core.database - INFO - Database connection successful
2025-07-03 19:43:56,254 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:43:56,254 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:43:56,255 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:43:56,257 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:56,257 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:56,259 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:56,259 - sqlalchemy.engine.Engine - INFO - [cached since 0.002472s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:56,260 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:56,260 - sqlalchemy.engine.Engine - INFO - [cached since 0.003295s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:56,261 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:56,261 - sqlalchemy.engine.Engine - INFO - [cached since 0.004351s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:56,261 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:56,262 - sqlalchemy.engine.Engine - INFO - [cached since 0.005086s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:56,262 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:56,262 - sqlalchemy.engine.Engine - INFO - [cached since 0.00585s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:56,263 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:43:56,263 - sqlalchemy.engine.Engine - INFO - [cached since 0.006522s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:43:56,263 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:43:56,264 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:43:56,264 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:43:56,265 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:43:56,265 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:43:56,265 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ()
2025-07-03 19:43:56,266 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:43:56,266 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:43:56,266 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:43:56,266 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:43:56,267 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:43:56,267 - main - INFO - Database initialized successfully
2025-07-03 19:44:15,921 - main - INFO - Shutting down Nexus application...
2025-07-03 19:44:16,453 - main - INFO - Starting Nexus application...
2025-07-03 19:44:16,477 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:44:16,477 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:44:16,478 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:44:16,479 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:44:16,480 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:44:16,480 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:44:16,480 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:44:16,481 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:44:16,481 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ()
2025-07-03 19:44:16,482 - app.core.database - INFO - Database connection successful
2025-07-03 19:44:16,482 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:44:16,482 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:44:16,482 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:44:16,484 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:16,484 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:16,486 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:16,487 - sqlalchemy.engine.Engine - INFO - [cached since 0.002741s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:16,487 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:16,487 - sqlalchemy.engine.Engine - INFO - [cached since 0.003484s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:16,488 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:16,488 - sqlalchemy.engine.Engine - INFO - [cached since 0.004141s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:16,488 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:16,489 - sqlalchemy.engine.Engine - INFO - [cached since 0.004817s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:16,489 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:16,489 - sqlalchemy.engine.Engine - INFO - [cached since 0.00551s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:16,490 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:16,490 - sqlalchemy.engine.Engine - INFO - [cached since 0.006124s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:16,490 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:44:16,491 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:44:16,491 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:44:16,492 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:44:16,492 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:44:16,492 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-03 19:44:16,492 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:44:16,493 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:44:16,493 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:44:16,493 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:44:16,493 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:44:16,494 - main - INFO - Database initialized successfully
2025-07-03 19:44:39,021 - main - INFO - Shutting down Nexus application...
2025-07-03 19:44:39,577 - main - INFO - Starting Nexus application...
2025-07-03 19:44:39,600 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:44:39,600 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:44:39,601 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:44:39,602 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:44:39,602 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:44:39,603 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:44:39,603 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:44:39,604 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:44:39,604 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ()
2025-07-03 19:44:39,605 - app.core.database - INFO - Database connection successful
2025-07-03 19:44:39,605 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:44:39,605 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:44:39,605 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:44:39,607 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:39,607 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:39,609 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:39,609 - sqlalchemy.engine.Engine - INFO - [cached since 0.0023s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:39,610 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:39,610 - sqlalchemy.engine.Engine - INFO - [cached since 0.002853s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:39,610 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:39,610 - sqlalchemy.engine.Engine - INFO - [cached since 0.003463s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:39,611 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:39,611 - sqlalchemy.engine.Engine - INFO - [cached since 0.004016s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:39,611 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:39,612 - sqlalchemy.engine.Engine - INFO - [cached since 0.004576s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:39,612 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:44:39,612 - sqlalchemy.engine.Engine - INFO - [cached since 0.00516s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:44:39,613 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:44:39,613 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:44:39,613 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-03 19:44:39,614 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:44:39,614 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:44:39,614 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:44:39,614 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:44:39,615 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:44:39,615 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:44:39,615 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:44:39,615 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:44:39,616 - main - INFO - Database initialized successfully
2025-07-03 19:45:10,146 - main - INFO - Shutting down Nexus application...
2025-07-03 19:45:10,725 - main - INFO - Starting Nexus application...
2025-07-03 19:45:10,748 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:45:10,748 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:45:10,750 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:45:10,750 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:45:10,750 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:45:10,751 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:45:10,751 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:45:10,752 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:45:10,752 - sqlalchemy.engine.Engine - INFO - [generated in 0.00030s] ()
2025-07-03 19:45:10,753 - app.core.database - INFO - Database connection successful
2025-07-03 19:45:10,753 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:45:10,753 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:45:10,754 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:45:10,756 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:10,756 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:10,758 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:10,758 - sqlalchemy.engine.Engine - INFO - [cached since 0.002779s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:10,759 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:10,759 - sqlalchemy.engine.Engine - INFO - [cached since 0.00359s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:10,760 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:10,760 - sqlalchemy.engine.Engine - INFO - [cached since 0.004153s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:10,760 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:10,760 - sqlalchemy.engine.Engine - INFO - [cached since 0.004733s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:10,761 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:10,761 - sqlalchemy.engine.Engine - INFO - [cached since 0.005323s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:10,761 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:10,761 - sqlalchemy.engine.Engine - INFO - [cached since 0.005798s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:10,762 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:45:10,762 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:45:10,762 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:45:10,763 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:45:10,763 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:45:10,763 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ()
2025-07-03 19:45:10,764 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:45:10,764 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:45:10,764 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ()
2025-07-03 19:45:10,765 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:45:10,765 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:45:10,765 - main - INFO - Database initialized successfully
2025-07-03 19:45:44,234 - main - INFO - Shutting down Nexus application...
2025-07-03 19:45:44,793 - main - INFO - Starting Nexus application...
2025-07-03 19:45:44,816 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-03 19:45:44,816 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:45:44,817 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-03 19:45:44,817 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:45:44,818 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-03 19:45:44,818 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-03 19:45:44,819 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-03 19:45:44,819 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-07-03 19:45:44,820 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ()
2025-07-03 19:45:44,820 - app.core.database - INFO - Database connection successful
2025-07-03 19:45:44,820 - sqlalchemy.engine.Engine - INFO - CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
2025-07-03 19:45:44,820 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] ()
2025-07-03 19:45:44,821 - app.core.database - INFO - TimescaleDB extension enabled
2025-07-03 19:45:44,823 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:44,823 - sqlalchemy.engine.Engine - INFO - [generated in 0.00034s] ('market_data', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:44,825 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:44,826 - sqlalchemy.engine.Engine - INFO - [cached since 0.002862s ago] ('events', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:44,826 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:44,826 - sqlalchemy.engine.Engine - INFO - [cached since 0.003465s ago] ('social_mentions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:44,827 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:44,827 - sqlalchemy.engine.Engine - INFO - [cached since 0.004048s ago] ('correlations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:44,827 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:44,827 - sqlalchemy.engine.Engine - INFO - [cached since 0.004659s ago] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:44,828 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:44,828 - sqlalchemy.engine.Engine - INFO - [cached since 0.005303s ago] ('alert_configs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:44,829 - sqlalchemy.engine.Engine - INFO - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-03 19:45:44,829 - sqlalchemy.engine.Engine - INFO - [cached since 0.006047s ago] ('data_sources', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-03 19:45:44,829 - app.core.database - INFO - Database tables created successfully
2025-07-03 19:45:44,829 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('market_data', 'time', if_not_exists => TRUE);
2025-07-03 19:45:44,830 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ()
2025-07-03 19:45:44,831 - app.core.database - INFO - market_data converted to hypertable
2025-07-03 19:45:44,831 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('events', 'time', if_not_exists => TRUE);
2025-07-03 19:45:44,831 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ()
2025-07-03 19:45:44,831 - app.core.database - INFO - events converted to hypertable
2025-07-03 19:45:44,832 - sqlalchemy.engine.Engine - INFO - SELECT create_hypertable('social_mentions', 'time', if_not_exists => TRUE);
2025-07-03 19:45:44,832 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-03 19:45:44,832 - app.core.database - INFO - social_mentions converted to hypertable
2025-07-03 19:45:44,832 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-03 19:45:44,833 - main - INFO - Database initialized successfully
